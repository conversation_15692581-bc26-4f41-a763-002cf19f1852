#!/usr/bin/env python3
"""
Debug script to isolate the 'unhashable type: dict' error in Tempo integration
"""

import json
import requests
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_tempo_instance(instance_name, api_key):
    """Test a single Tempo instance"""
    print(f"\n🔍 Testing {instance_name}")
    print("-" * 40)
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    # Calculate date range
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=1)
    
    params = {
        'from': start_date.strftime('%Y-%m-%d'),
        'to': end_date.strftime('%Y-%m-%d'),
        'limit': 1000
    }
    
    try:
        url = "https://api.tempo.io/4/worklogs"
        print(f"📡 Making request to: {url}")
        print(f"📅 Date range: {start_date} to {end_date}")
        
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            worklogs = data.get('results', [])
            print(f"✅ Found {len(worklogs)} worklogs")
            
            # Process each worklog
            users = {}
            for i, worklog in enumerate(worklogs):
                try:
                    print(f"\n📝 Processing worklog {i+1}/{len(worklogs)}")
                    
                    # Extract basic info
                    author_info = worklog.get('author', {})
                    account_id = author_info.get('accountId', '')
                    display_name = author_info.get('displayName', '')
                    
                    print(f"   Author: {display_name} ({account_id})")
                    
                    # Extract issue info
                    issue = worklog.get('issue', {})
                    issue_key = issue.get('key', '') if issue else ''
                    
                    print(f"   Issue: {issue_key}")
                    print(f"   Issue type: {type(issue)}")
                    
                    # Extract description
                    description = worklog.get('description', '')
                    print(f"   Description: {description[:50]}...")
                    
                    # Try regex pattern
                    if not issue_key and description:
                        import re
                        print(f"   Trying regex extraction...")
                        ticket_pattern = re.compile(r'(CHIL|XDPA|SCRUM)-\d+', re.IGNORECASE)
                        full_matches = ticket_pattern.findall(description)
                        if full_matches:
                            extracted_key = full_matches[0].upper()
                            print(f"   Extracted key: {extracted_key}")
                            print(f"   Extracted type: {type(extracted_key)}")
                        else:
                            print(f"   No key extracted")
                    
                    # Track user
                    if account_id:
                        if account_id not in users:
                            users[account_id] = 0
                        users[account_id] += 1
                    
                except Exception as e:
                    print(f"   ❌ Error processing worklog {i+1}: {e}")
                    print(f"   Worklog structure: {list(worklog.keys())}")
                    import traceback
                    traceback.print_exc()
                    break
            
            print(f"\n📊 Summary for {instance_name}:")
            print(f"   Total worklogs: {len(worklogs)}")
            print(f"   Unique users: {len(users)}")
            for user_id, count in users.items():
                print(f"     {user_id}: {count} worklogs")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Test all Tempo instances"""
    print("🔍 Tempo Error Debug Tool")
    print("=" * 50)
    
    # Test each instance
    instances = [
        ("Childfree Legacy", os.getenv('TEMPO_API_KEY', '')),
        ("ConvX XDP", os.getenv('TEMPO_CONVX_API_KEY', '')),
        ("TopProperty", os.getenv('TEMPO_TOPPROPERTY_API_KEY', ''))
    ]
    
    for name, api_key in instances:
        if api_key:
            test_tempo_instance(name, api_key)
        else:
            print(f"\n⚠️ {name}: No API key found")

if __name__ == "__main__":
    main()
