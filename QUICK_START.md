# Quick Start Guide

Get your developer performance analyzer running in 5 minutes!

## 📋 Prerequisites

- Python 3.7+
- Git installed and configured
- Access to your Git repositories

## ⚡ 5-Minute Setup

### 1. Install Dependencies
```bash
pip install python-dotenv requests
```

### 2. Create Environment File
Create a `.env` file in the project root:
```bash
# .env
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
SLACK_TOKEN=xoxb-your-slack-token
SLACK_CHANNEL_ID=C09ANDRBK7C
```

### 3. Update config.json
Make sure your `config.json` has your projects:
```json
{
  "projects": [
    {
      "name": "Your Project",
      "repository_url": "**************:user/repo.git",
      "local_path": "./repositories/your_project",
      "enabled": true
    }
  ]
}
```

### 4. Test the System
```bash
# Test Slack connection
python3 daily_analyzer.py --test-slack

# Run analysis for yesterday
python3 daily_analyzer.py
```

## 🎯 Common Commands

### Daily Usage
```bash
# Analyze yesterday (most common)
python3 daily_analyzer.py

# Analyze specific date
python3 daily_analyzer.py --date 2025-08-15
```

### Weekly Reports
```bash
# Last 7 days
python3 daily_analyzer.py --days-back 7

# Specific week
python3 daily_analyzer.py --date-range 2025-08-11:2025-08-17
```

### Automation
```bash
# Test cron script
python3 cron_daily_analysis.py --test

# Setup daily automation at 9:00 AM
crontab -e
# Add: 0 9 * * * cd /path/to/git-analyzer && python3 cron_daily_analysis.py
```

## 📊 What You Get

### Individual Developer Reports
- **Performance Rating**: 0-5 scale based on code complexity analysis
- **Complexity Score**: Technical impact measurement (e.g., Ivan D: 298, Dmytro: 61)
- **Technical Focus**: Frontend/backend/database from actual file changes
- **Code Quality**: Security, error handling, testing patterns found in diffs
- **Meaningful Changes**: Percentage of substantial vs formatting changes

### Team Insights
- **Highest Technical Impact**: Based on complexity scores, not commit count
- **Technical Areas**: Actual areas worked on (frontend/backend/database)
- **Code Quality Focus**: Testing/security patterns found in code diffs

### Smart Features
- **Developer Merging**: Combines aliases (e.g., `john.doe` + `j.doe` = `John Doe`)
- **Bot Filtering**: Automatically excludes `gpt-engineer-app[bot]`, `dependabot[bot]`, etc.
- **All Branches**: Analyzes commits from all branches, not just main/master
- **Diff Analysis**: Looks at actual code changes, not just commit messages

## 🔧 Troubleshooting

### No Commits Found
```bash
# Check if repositories are accessible
python3 setup.py --test-only

# Try a different date
python3 daily_analyzer.py --date 2025-08-14
```

### Slack Not Working
```bash
# Test Slack connection
python3 daily_analyzer.py --test-slack

# Check your .env file has correct tokens
```

### AI Analysis Failed
- Reports will still be generated with basic statistics
- Check your OpenRouter API key in `.env`
- Slack notifications are skipped if AI fails (configurable)

## 📁 Output Files

- **Reports**: `./reports/daily_report_YYYY-MM-DD.txt`
- **Logs**: `./logs/dev_analyzer.log`
- **Slack**: Formatted messages sent to your channel

## 🚀 Next Steps

1. **Add More Projects**: Update `config.json` with additional repositories
2. **Setup Automation**: Configure cron job for daily reports
3. **Customize Mapping**: Add developer aliases in `developer_mapping.global`
4. **Monitor Logs**: Check `./logs/` for any issues

That's it! You're ready to analyze developer performance across your projects! 🎉
