#!/usr/bin/env python3
"""
Test script to verify Tempo user mapping works for all developers
"""

import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from jira_integration import JiraTempoAnalyzer

def test_tempo_mapping():
    """Test that all developers get proper Tempo mapping"""
    print("🧪 Testing Tempo User Mapping...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    analyzer = JiraTempoAnalyzer(config)
    
    # Test the mapping function with various user names
    test_users = [
        "<PERSON><PERSON><PERSON>",
        "<PERSON>", 
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON> Sidorenko",
        "User-cc7fe9a0",
        "User-8cf57eb6",
        "712020:793f4892-002c-41b1-a76e-3c49cc7fe9a0",
        "Some Unknown User",
        "r<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>lav",
        "<PERSON> D",
        "<PERSON><PERSON><PERSON>"
    ]
    
    print("🔍 Testing Tempo User Mapping:")
    print("=" * 60)
    
    for user in test_users:
        mapped = analyzer._map_tempo_user_to_developer(user)
        status = "✅" if mapped != user else "⚠️"
        print(f"{status} '{user}' → '{mapped}'")
    
    # Test with real Tempo data if available
    if analyzer.tempo.is_configured():
        print("\n📊 Testing with Real Tempo Data:")
        print("=" * 40)
        
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=7)
        
        try:
            tempo_data = analyzer.tempo.get_user_worklogs(start_date, end_date)
            aggregated = analyzer.tempo.aggregate_time_by_developer(tempo_data)
            
            print(f"Found {len(aggregated)} users in Tempo data:")
            
            for tempo_user, data in aggregated.items():
                mapped = analyzer._map_tempo_user_to_developer(tempo_user)
                hours = data.get('total_hours', 0)
                tickets = len(data.get('unique_tickets', []))
                
                status = "✅" if mapped != tempo_user else "⚠️"
                print(f"{status} '{tempo_user}' → '{mapped}' ({hours:.1f}h, {tickets} tickets)")
                
        except Exception as e:
            print(f"❌ Error getting real Tempo data: {e}")
    
    else:
        print("\n⚠️ Tempo not configured - skipping real data test")

def test_full_analysis():
    """Test full analysis to see if all developers get time tracking"""
    print("\n🔬 Testing Full Analysis...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    analyzer = JiraTempoAnalyzer(config)
    
    # Create test commits from all developers
    test_commits = [
        {'author': 'Oleksii Stupak', 'message': 'CHIL-123: Test commit', 'date': '2025-08-15'},
        {'author': 'Ivan D', 'message': 'XDPA-456: Test commit', 'date': '2025-08-15'},
        {'author': 'Dmytro Haidamachenko', 'message': 'CHIL-789: Test commit', 'date': '2025-08-15'},
        {'author': 'Rostyslav F', 'message': 'XDPA-101: Test commit', 'date': '2025-08-15'},
        {'author': 'Dmytro Marjan', 'message': 'SCRUM-202: Test commit', 'date': '2025-08-15'},
        {'author': 'Maxim Sidorenko', 'message': 'CHIL-303: Test commit', 'date': '2025-08-15'},
    ]
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=1)
    
    try:
        results = analyzer.analyze_developer_tickets_and_time(test_commits, start_date, end_date)
        
        print("📋 Analysis Results:")
        print("=" * 50)
        
        for developer, data in results.items():
            has_commits = data.get('has_commits', False)
            has_time_logs = data.get('has_time_logs', False)
            tempo_data = data.get('tempo_data', {})
            total_hours = tempo_data.get('total_hours', 0)
            
            commit_status = "✅" if has_commits else "❌"
            time_status = "✅" if has_time_logs else "❌"
            
            print(f"🧑‍💻 {developer}")
            print(f"   Commits: {commit_status} ({len(data.get('commit_tickets', []))} tickets)")
            print(f"   Time Tracking: {time_status} ({total_hours:.1f}h logged)")
            print()
        
        # Summary
        developers_with_time = sum(1 for d in results.values() if d.get('has_time_logs', False))
        total_developers = len(results)
        
        print(f"📊 Summary: {developers_with_time}/{total_developers} developers have time tracking data")
        
        if developers_with_time < total_developers:
            print("\n⚠️ Some developers missing time tracking data!")
            print("Missing developers:")
            for dev, data in results.items():
                if not data.get('has_time_logs', False):
                    print(f"   - {dev}")
        else:
            print("\n✅ All developers have time tracking data!")
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests"""
    print("🧪 Tempo Mapping Test Suite")
    print("=" * 60)
    
    try:
        test_tempo_mapping()
        test_full_analysis()
        
        print("\n📋 Recommendations:")
        print("1. If any users show ⚠️, add them to tempo mapping in config.json")
        print("2. Run: python3 daily_analyzer.py --date 2025-08-15")
        print("3. Verify all developers show 'Time Tracked' field in report")
        print("4. Check Slack report for complete time tracking data")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
