#!/usr/bin/env python3
"""
Jira and Tempo Integration Module
Tracks tickets worked on by developers and time logged in Tempo
"""

import requests
import json
import base64
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import re
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class JiraIntegration:
    """Handles Jira API interactions for ticket tracking with multiple instances support"""

    def __init__(self, config: Dict):
        self.config = config
        self.enabled = True  # Enable if any projects have Jira config
        self.instances = []

        # Read Jira instances from projects
        projects = config.get('projects', [])
        for project in projects:
            if not project.get('enabled', True):
                continue

            jira_config = project.get('jira')
            if jira_config:
                # Create instance config from project
                instance_config = {
                    'name': project['name'],
                    'base_url': jira_config['base_url'],
                    'api_key': jira_config['api_key'],
                    'email': jira_config['email'],
                    'project_key': jira_config['project_key'],
                    'board_id': jira_config['board_id']
                }
                instance = self._create_instance(instance_config)
                if instance:
                    self.instances.append(instance)

        # Disable if no instances found
        if not self.instances:
            self.enabled = False

    def _create_instance(self, instance_config: Dict) -> Optional[Dict]:
        """Create a Jira instance configuration"""
        try:
            base_url = instance_config.get('base_url', '')
            api_key_env = instance_config.get('api_key', '').replace('${', '').replace('}', '')
            email_env = instance_config.get('email', '').replace('${', '').replace('}', '')

            # Handle different API key environment variables
            if 'CONVX' in api_key_env.upper():
                api_key = os.getenv('JIRA_CONVX_API_KEY', instance_config.get('api_key', ''))
            else:
                api_key = os.getenv('JIRA_API_KEY', instance_config.get('api_key', ''))

            email = os.getenv('JIRA_EMAIL', instance_config.get('email', ''))

            if not all([base_url, api_key, email]):
                logger.warning(f"Incomplete Jira configuration for {instance_config.get('name', 'Unknown')}")
                return None

            # Create auth header
            auth_string = f"{email}:{api_key}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

            return {
                'name': instance_config.get('name', 'Default'),
                'base_url': base_url,
                'api_key': api_key,
                'email': email,
                'project_key': instance_config.get('project_key', 'PROJ'),
                'board_id': instance_config.get('board_id', 1),
                'repositories': instance_config.get('repositories', []),
                'headers': {
                    'Authorization': f'Basic {auth_b64}',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }
        except Exception as e:
            logger.error(f"Error creating Jira instance: {e}")
            return None
    
    def is_configured(self) -> bool:
        """Check if Jira is properly configured"""
        return self.enabled and len(self.instances) > 0

    def get_instance_for_repository(self, repository_url: str) -> Optional[Dict]:
        """Get the appropriate Jira instance for a repository"""
        for instance in self.instances:
            if repository_url in instance.get('repositories', []):
                return instance
        # Return first instance as fallback
        return self.instances[0] if self.instances else None

    def _get_instance_for_ticket(self, ticket_id: str) -> Optional[Dict]:
        """Get the appropriate Jira instance for a ticket based on project key"""
        project_key = ticket_id.split('-')[0] if '-' in ticket_id else ''
        for instance in self.instances:
            if instance['project_key'] == project_key:
                return instance
        # Return first instance as fallback
        return self.instances[0] if self.instances else None
    
    def extract_ticket_ids_from_commits(self, commits: List[Dict], repository_url: str = None) -> Dict[str, List[str]]:
        """Extract Jira ticket IDs from commit messages"""
        developer_tickets = {}

        # Get all project keys from all instances
        project_keys = [instance['project_key'] for instance in self.instances]

        # Create pattern for all project keys
        if project_keys:
            pattern_str = '|'.join([f'{key}-\\d+' for key in project_keys])
            ticket_pattern = re.compile(f'({pattern_str})', re.IGNORECASE)
        else:
            return developer_tickets

        for commit in commits:
            author = commit['author']
            message = commit['message']

            # Find all ticket IDs in the commit message
            tickets = ticket_pattern.findall(message)
            tickets = [ticket.upper() for ticket in tickets]  # Normalize to uppercase

            if tickets:
                if author not in developer_tickets:
                    developer_tickets[author] = []
                developer_tickets[author].extend(tickets)

        # Remove duplicates while preserving order
        for author in developer_tickets:
            seen = set()
            unique_tickets = []
            for ticket in developer_tickets[author]:
                if ticket not in seen:
                    seen.add(ticket)
                    unique_tickets.append(ticket)
            developer_tickets[author] = unique_tickets

        return developer_tickets
    
    def get_ticket_details(self, ticket_ids: List[str]) -> Dict[str, Dict]:
        """Get detailed information for multiple tickets from appropriate instances"""
        if not self.is_configured() or not ticket_ids:
            return {}

        ticket_details = {}

        for ticket_id in ticket_ids:
            # Find the appropriate instance for this ticket
            instance = self._get_instance_for_ticket(ticket_id)
            if not instance:
                logger.warning(f"No Jira instance found for ticket {ticket_id}")
                continue

            try:
                url = f"{instance['base_url']}/rest/api/3/issue/{ticket_id}"
                response = requests.get(url, headers=instance['headers'], timeout=10)

                if response.status_code == 200:
                    issue_data = response.json()

                    ticket_details[ticket_id] = {
                        'key': issue_data['key'],
                        'summary': issue_data['fields']['summary'],
                        'status': issue_data['fields']['status']['name'],
                        'priority': issue_data['fields']['priority']['name'] if issue_data['fields']['priority'] else 'None',
                        'issue_type': issue_data['fields']['issuetype']['name'],
                        'assignee': issue_data['fields']['assignee']['displayName'] if issue_data['fields']['assignee'] else 'Unassigned',
                        'created': issue_data['fields']['created'],
                        'updated': issue_data['fields']['updated'],
                        'jira_instance': instance['name']
                    }

                    logger.debug(f"Retrieved details for ticket {ticket_id} from {instance['name']}")
                else:
                    logger.warning(f"Failed to get details for ticket {ticket_id}: {response.status_code}")
                    ticket_details[ticket_id] = {
                        'key': ticket_id,
                        'summary': 'Unable to retrieve',
                        'status': 'Unknown',
                        'priority': 'Unknown',
                        'issue_type': 'Unknown',
                        'assignee': 'Unknown',
                        'created': '',
                        'updated': '',
                        'jira_instance': instance['name']
                    }

            except Exception as e:
                logger.error(f"Error retrieving ticket {ticket_id}: {str(e)}")
                ticket_details[ticket_id] = {
                    'key': ticket_id,
                    'summary': 'Error retrieving',
                    'status': 'Error',
                    'priority': 'Unknown',
                    'issue_type': 'Unknown',
                    'assignee': 'Unknown',
                    'created': '',
                    'updated': ''
                }
        
        return ticket_details


class TempoIntegration:
    """Handles Tempo API interactions for time tracking with multiple API keys"""

    def __init__(self, config: Dict):
        self.config = config
        self.tempo_config = config.get('tempo', {})
        self.base_url = 'https://api.tempo.io/4'

        # Extract Tempo API keys from projects
        self.tempo_keys = {}
        projects = config.get('projects', [])

        # Process projects to find Tempo configurations
        for project in projects:
                if not project.get('enabled', True):
                    continue

                tempo_config = project.get('tempo')
                if tempo_config:
                    instance_name = project['name']
                    tempo_api_key = tempo_config.get('api_key', '')

                    logger.debug(f"Processing Tempo instance: {instance_name}, tempo_api_key: {tempo_api_key[:20]}...")

                    if tempo_api_key:
                        # Check if it's an environment variable or already substituted
                        if tempo_api_key.startswith('${') and tempo_api_key.endswith('}'):
                            # Extract environment variable name
                            env_var_name = tempo_api_key.replace('${', '').replace('}', '')
                            api_key = os.getenv(env_var_name, '')
                            logger.debug(f"Using env var {env_var_name}, found: {bool(api_key)}")
                        else:
                            # Already substituted value
                            api_key = tempo_api_key
                            logger.debug(f"Using direct API key value for {instance_name}")

                        if api_key:
                            # Get repositories from git config
                            git_config = project.get('git', {})
                            repositories = []
                            if 'repositories' in git_config:
                                repositories = [repo['repository_url'] for repo in git_config['repositories']]
                            elif 'repository_url' in git_config:
                                repositories = [git_config['repository_url']]

                            self.tempo_keys[instance_name] = {
                                'api_key': api_key,
                                'headers': {
                                    'Authorization': f'Bearer {api_key}',
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json'
                                },
                                'repositories': repositories
                            }
                            logger.debug(f"Added Tempo instance: {instance_name}")
                        else:
                            logger.debug(f"No valid API key found for {instance_name}")
                    else:
                        logger.debug(f"No tempo_api_key configured for instance: {instance_name}")

        # Fallback to legacy single API key
        if not self.tempo_keys:
            logger.debug("No Tempo instances found, falling back to legacy single API key")
            legacy_key = os.getenv('TEMPO_API_KEY', self.tempo_config.get('api_key', ''))
            if legacy_key:
                logger.debug("Using legacy TEMPO_API_KEY for default instance")
                self.tempo_keys['default'] = {
                    'api_key': legacy_key,
                    'headers': {
                        'Authorization': f'Bearer {legacy_key}',
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    'repositories': []
                }
            else:
                logger.debug("No legacy TEMPO_API_KEY found either")
            
        # Enable if we found any Tempo keys
        self.enabled = len(self.tempo_keys) > 0
        logger.debug(f"Tempo enabled: {self.enabled}")
        logger.debug(f"Final Tempo instances configured: {list(self.tempo_keys.keys())}")
    
    def is_configured(self) -> bool:
        """Check if Tempo is properly configured"""
        return self.enabled and len(self.tempo_keys) > 0
    
    def get_user_worklogs(self, start_date: datetime.date, end_date: datetime.date,
                         developer_emails: List[str] = None) -> Dict[str, List[Dict]]:
        """Get work logs for developers in the specified date range from all Tempo instances"""
        if not self.is_configured():
            return {}

        all_worklogs = {}

        # Query each Tempo instance
        for instance_name, tempo_config in self.tempo_keys.items():
            logger.info(f"Querying Tempo instance: {instance_name}")

            try:
                # Format dates for API
                from_date = start_date.strftime('%Y-%m-%d')
                to_date = end_date.strftime('%Y-%m-%d')

                # Build query parameters
                params = {
                    'from': from_date,
                    'to': to_date,
                    'limit': 1000  # Adjust as needed
                }

                url = f"{self.base_url}/worklogs"
                logger.debug(f"Tempo request -> instance={instance_name} url={url} params={params}")
                response = requests.get(url, headers=tempo_config['headers'], params=params, timeout=15)

                logger.debug(f"Tempo response -> instance={instance_name} status={response.status_code}")
                if response.status_code == 200:
                    worklogs_data = response.json()
                    if not isinstance(worklogs_data, dict):
                        logger.error(f"Tempo response JSON is not a dict for instance={instance_name}: type={type(worklogs_data)}")
                        continue
                    if 'results' not in worklogs_data:
                        logger.error(f"Tempo response missing 'results' key for instance={instance_name}: keys={list(worklogs_data.keys())}")
                        continue
                    worklogs = worklogs_data.get('results', [])
                    logger.debug(f"Tempo parsed worklogs -> instance={instance_name} count={len(worklogs)}")

                    # Cache for resolved user info
                    user_cache = {}

                    for idx, worklog in enumerate(worklogs):
                        try:
                            logger.debug(f"Processing worklog idx={idx} instance={instance_name} keys={list(worklog.keys())}")
                            author_info = worklog.get('author', {})
                            account_id = author_info.get('accountId', '')

                            # Use Tempo mapping directly instead of resolving from Jira
                            tempo_mapping = self.config.get('developer_mapping', {}).get('tempo', {})
                            author_name = tempo_mapping.get(account_id, f'User-{account_id[-8:]}' if account_id else 'Unknown')
                            author_email = ''  # Not needed for our use case

                            # Filter by developer emails if provided
                            if developer_emails and author_email not in developer_emails:
                                logger.debug(f"Skipping worklog idx={idx} due to developer_emails filter")
                                continue

                            if author_name not in all_worklogs:
                                all_worklogs[author_name] = []

                            # Parse worklog data
                            issue = worklog.get('issue', {})
                            issue_key = issue.get('key', '') if isinstance(issue, dict) else ''
                            description = worklog.get('description', '')

                            # If no issue key, try to extract from description
                            if not issue_key and description:
                                # Look for ticket patterns in description (support multiple project keys)
                                import re
                                ticket_pattern = re.compile(r'(?:CHIL|XDPA|SCRUM)-\d+', re.IGNORECASE)
                                matches = ticket_pattern.findall(description)
                                if matches:
                                    issue_key = matches[0].upper()

                            # Ensure issue_key is a string (safety check)
                            if isinstance(issue_key, dict) or issue_key is None:
                                issue_key = ''
                            else:
                                issue_key = str(issue_key)

                            worklog_entry = {
                                'issue_key': issue_key,
                                'time_spent_seconds': worklog.get('timeSpentSeconds', 0),
                                'time_spent_hours': worklog.get('timeSpentSeconds', 0) / 3600,
                                'date': worklog.get('startDate', ''),
                                'description': description,
                                'author': author_name,
                                'author_email': author_email,
                                'tempo_instance': instance_name
                            }

                            all_worklogs[author_name].append(worklog_entry)
                        except Exception as e:
                            logger.exception(f"Error processing Tempo worklog in instance={instance_name} idx={idx}: {e}")
                            continue

                    try:
                        unique_authors = len(set((w.get('author') or 'Unknown') for w in worklogs))
                    except Exception:
                        unique_authors = len(worklogs)
                    logger.info(f"Retrieved worklogs from {instance_name} for {unique_authors} developers")

                else:
                    logger.error(f"Failed to get Tempo worklogs from {instance_name}: {response.status_code} - {response.text}")

            except Exception as e:
                logger.error(f"Error retrieving Tempo worklogs from {instance_name}: {str(e)}")
                continue

        logger.info(f"Retrieved total worklogs for {len(all_worklogs)} developers from {len(self.tempo_keys)} instances")
        return all_worklogs
    
    def aggregate_time_by_developer(self, developer_worklogs: Dict[str, List[Dict]]) -> Dict[str, Dict]:
        """Aggregate time logging data by developer"""
        aggregated_data = {}

        for developer, worklogs in developer_worklogs.items():
            total_hours = sum(log['time_spent_hours'] for log in worklogs)
            # Safety check: ensure issue_key is a string before adding to set
            unique_tickets = set()
            for log in worklogs:
                issue_key = log.get('issue_key', '')
                if issue_key and isinstance(issue_key, str):
                    unique_tickets.add(issue_key)

            # Calculate time per ticket and collect descriptions
            time_per_ticket = {}
            ticket_descriptions = {}
            general_descriptions = []  # For worklogs without specific tickets

            for log in worklogs:
                ticket = log['issue_key']
                description = log['description']

                if ticket:
                    # Aggregate time for specific tickets
                    if ticket not in time_per_ticket:
                        time_per_ticket[ticket] = 0
                        ticket_descriptions[ticket] = []
                    time_per_ticket[ticket] += log['time_spent_hours']

                    # Collect descriptions for specific tickets
                    if description:
                        # Clean up description (remove ticket number if it's at the start)
                        clean_desc = description
                        if clean_desc.upper().startswith(ticket.upper()):
                            clean_desc = clean_desc[len(ticket):].strip()
                            if clean_desc.startswith(':') or clean_desc.startswith('-'):
                                clean_desc = clean_desc[1:].strip()

                        # Don't truncate - show full description
                        if clean_desc and clean_desc not in ticket_descriptions[ticket]:
                            ticket_descriptions[ticket].append(clean_desc)
                else:
                    # Collect general work descriptions (no specific ticket)
                    if description and description not in general_descriptions:
                        general_descriptions.append(description)

            # Round time per ticket
            time_per_ticket = {ticket: round(hours, 2) for ticket, hours in time_per_ticket.items()}

            aggregated_data[developer] = {
                'total_hours_logged': round(total_hours, 2),
                'total_worklogs': len(worklogs),
                'unique_tickets': list(unique_tickets),
                'tickets_count': len(unique_tickets),
                'time_per_ticket': time_per_ticket,
                'ticket_descriptions': ticket_descriptions,
                'general_descriptions': general_descriptions,
                'worklogs': worklogs
            }

        return aggregated_data

    def _resolve_user_info(self, account_id: str) -> Dict:
        """Resolve user info from Jira using account ID"""
        try:
            # Get Jira config for API call
            jira_config = self.config.get('jira', {})
            if not jira_config.get('enabled', False):
                return {
                    'displayName': f'User-{account_id[-8:]}',
                    'emailAddress': '',
                    'accountId': account_id
                }

            # Use Jira API to get user info
            base_url = jira_config.get('base_url', '')
            api_key = os.getenv('JIRA_API_KEY', jira_config.get('api_key', ''))
            email = os.getenv('JIRA_EMAIL', jira_config.get('email', ''))

            if not all([base_url, api_key, email]):
                return {
                    'displayName': f'User-{account_id[-8:]}',
                    'emailAddress': '',
                    'accountId': account_id
                }

            # Create auth header
            auth_string = f"{email}:{api_key}"
            auth_bytes = auth_string.encode('ascii')
            auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
            headers = {
                'Authorization': f'Basic {auth_b64}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }

            # Call Jira API to get user info
            url = f"{base_url}/rest/api/3/user"
            params = {'accountId': account_id}
            response = requests.get(url, headers=headers, params=params, timeout=10)

            if response.status_code == 200:
                user_data = response.json()
                return {
                    'displayName': user_data.get('displayName', f'User-{account_id[-8:]}'),
                    'emailAddress': user_data.get('emailAddress', ''),
                    'accountId': account_id
                }
            else:
                logger.warning(f"Failed to get user info for {account_id}: {response.status_code}")
                return {
                    'displayName': f'User-{account_id[-8:]}',
                    'emailAddress': '',
                    'accountId': account_id
                }

        except Exception as e:
            logger.error(f"Error resolving user info for {account_id}: {str(e)}")
            return {
                'displayName': f'User-{account_id[-8:]}',
                'emailAddress': '',
                'accountId': account_id
            }


class JiraTempoAnalyzer:
    """Main class that combines Jira and Tempo analysis"""
    
    def __init__(self, config: Dict):
        self.jira = JiraIntegration(config)
        self.tempo = TempoIntegration(config)
        self.developer_mapping = config.get('developer_mapping', {}).get('git', {})
        self.tempo_mapping = config.get('developer_mapping', {}).get('tempo', {})
    
    def analyze_developer_tickets_and_time(self, commits: List[Dict], 
                                         start_date: datetime.date, 
                                         end_date: datetime.date) -> Dict[str, Dict]:
        """Analyze both ticket involvement and time logging for developers"""
        analysis_results = {}
        
        # Extract tickets from commits
        commit_tickets = self.jira.extract_ticket_ids_from_commits(commits) if self.jira.is_configured() else {}
        
        # Get ticket details
        all_tickets = []
        for tickets in commit_tickets.values():
            all_tickets.extend(tickets)
        unique_tickets = list(set(all_tickets))
        
        ticket_details = self.jira.get_ticket_details(unique_tickets) if self.jira.is_configured() else {}
        
        # Get time logging data
        tempo_data = self.tempo.get_user_worklogs(start_date, end_date) if self.tempo.is_configured() else {}
        aggregated_tempo = self.tempo.aggregate_time_by_developer(tempo_data) if tempo_data else {}
        
        # Apply Tempo user mapping to aggregated data with improved matching
        mapped_tempo_data = {}
        for tempo_user, data in aggregated_tempo.items():
            mapped_name = self._map_tempo_user_to_developer(tempo_user)

            # Merge data if developer already exists (multiple Tempo accounts for same person)
            if mapped_name in mapped_tempo_data:
                # Merge the data
                existing_data = mapped_tempo_data[mapped_name]
                merged_data = {
                    'total_hours': existing_data.get('total_hours', 0) + data.get('total_hours', 0),
                    'unique_tickets': list(set(existing_data.get('unique_tickets', []) + data.get('unique_tickets', []))),
                    'time_per_ticket': {**existing_data.get('time_per_ticket', {}), **data.get('time_per_ticket', {})},
                    'ticket_descriptions': {**existing_data.get('ticket_descriptions', {}), **data.get('ticket_descriptions', {})},
                    'general_descriptions': existing_data.get('general_descriptions', []) + data.get('general_descriptions', []),
                    'worklogs': existing_data.get('worklogs', []) + data.get('worklogs', [])
                }
                mapped_tempo_data[mapped_name] = merged_data
            else:
                mapped_tempo_data[mapped_name] = data

        # Combine data for each developer
        all_developers = set(commit_tickets.keys()) | set(mapped_tempo_data.keys())

        for developer in all_developers:
            # Normalize developer name for commits
            normalized_name = self.developer_mapping.get(developer, developer)

            # Get commit data for this developer (check both original and normalized names)
            dev_commit_tickets = commit_tickets.get(developer, [])
            if not dev_commit_tickets and developer != normalized_name:
                dev_commit_tickets = commit_tickets.get(normalized_name, [])

            # Get tempo data for this developer (check both original and normalized names)
            dev_tempo_data = mapped_tempo_data.get(developer, {})
            if not dev_tempo_data and developer != normalized_name:
                dev_tempo_data = mapped_tempo_data.get(normalized_name, {})

            # Ensure tempo_data has default structure even if empty
            if not dev_tempo_data:
                dev_tempo_data = {
                    'total_hours_logged': 0,
                    'total_worklogs': 0,
                    'unique_tickets': [],
                    'tickets_count': 0,
                    'time_per_ticket': {},
                    'ticket_descriptions': {},
                    'general_descriptions': [],
                    'worklogs': []
                }

            analysis_results[normalized_name] = {
                'commit_tickets': dev_commit_tickets,
                'ticket_details': {tid: ticket_details.get(tid, {}) for tid in dev_commit_tickets},
                'tempo_data': dev_tempo_data,
                'has_commits': len(dev_commit_tickets) > 0,
                'has_time_logs': dev_tempo_data.get('total_hours_logged', 0) > 0
            }
        
        return analysis_results

    def _map_tempo_user_to_developer(self, tempo_user: str) -> str:
        """Improved mapping of Tempo user to developer name"""
        # Direct mapping first
        if tempo_user in self.tempo_mapping:
            mapped_name = self.tempo_mapping[tempo_user]
            return self.developer_mapping.get(mapped_name, mapped_name)

        # Try global mapping directly
        if tempo_user in self.developer_mapping:
            return self.developer_mapping[tempo_user]

        # Fuzzy matching for common patterns
        tempo_lower = tempo_user.lower()

        # Check if any global mapping key matches
        for git_name, mapped_name in self.developer_mapping.items():
            if git_name.lower() in tempo_lower or tempo_lower in git_name.lower():
                return mapped_name

        # Check if any tempo mapping value matches
        for tempo_name, mapped_name in self.tempo_mapping.items():
            if mapped_name.lower() in tempo_lower or tempo_lower in mapped_name.lower():
                return mapped_name

        # Name-based fuzzy matching
        for mapped_name in set(self.developer_mapping.values()):
            # Split names and check for matches
            tempo_parts = tempo_lower.split()
            mapped_parts = mapped_name.lower().split()

            # If first name matches
            if tempo_parts and mapped_parts and tempo_parts[0] == mapped_parts[0]:
                return mapped_name

            # If any significant part matches (length > 2)
            for tempo_part in tempo_parts:
                for mapped_part in mapped_parts:
                    if len(tempo_part) > 2 and len(mapped_part) > 2 and tempo_part == mapped_part:
                        return mapped_name

        # Return original name if no mapping found
        return tempo_user
