#!/bin/bash

echo "🚀 Aleannlab Tempo User Discovery"
echo "================================="

# Check if .env file exists
if [ -f .env ]; then
    echo "✅ Found .env file, loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️ No .env file found. Make sure TEMPO_ALEANNLAB_API_KEY is set in your environment."
fi

# Check if the required environment variables are set
if [ -z "$TEMPO_ALEANNLAB_API_KEY" ]; then
    echo "❌ TEMPO_ALEANNLAB_API_KEY is not set!"
    echo ""
    echo "Please add it to your .env file:"
    echo "TEMPO_ALEANNLAB_API_KEY=your_api_key_here"
    echo ""
    echo "Based on your memories, the API key should be:"
    echo "ATATT3xFfGF0EvE_d87QsesavsDdTiceSkANhr1iELtItSrEJ6os5wUpk46URlU8B4kqVyTgteap36098hfl1v4Ei3auMICf26TcZmKZtehe9MaBJTg7kBcL3Hdb4VKwzCR8QTxzMzlfHtJKq3ZPDokRELRVlniXrzrWN17kDCvAd_JIIXvOWGs=AA5FC415"
    exit 1
fi

echo "✅ TEMPO_ALEANNLAB_API_KEY is set"

# Check for Jira credentials (optional but recommended)
if [ -z "$JIRA_ALEANNLAB_API_KEY" ] || [ -z "$JIRA_ALEANNLAB_EMAIL" ]; then
    echo "⚠️ JIRA_ALEANNLAB_API_KEY or JIRA_ALEANNLAB_EMAIL not set"
    echo "   User names will not be resolved from Jira (will show as 'Unknown')"
    echo ""
    echo "To resolve user names, add to your .env file:"
    echo "JIRA_ALEANNLAB_API_KEY=ATATT3xFfGF0EvE_d87QsesavsDdTiceSkANhr1iELtItSrEJ6os5wUpk46URlU8B4kqVyTgteap36098hfl1v4Ei3auMICf26TcZmKZtehe9MaBJTg7kBcL3Hdb4VKwzCR8QTxzMzlfHtJKq3ZPDokRELRVlniXrzrWN17kDCvAd_JIIXvOWGs=AA5FC415"
    echo "JIRA_ALEANNLAB_EMAIL=<EMAIL>"
else
    echo "✅ JIRA_ALEANNLAB_API_KEY and JIRA_ALEANNLAB_EMAIL are set"
fi
echo ""

# Run the Python script
python3 fetch_aleannlab_tempo_users.py
