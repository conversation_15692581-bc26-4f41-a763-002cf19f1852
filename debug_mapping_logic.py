#!/usr/bin/env python3
"""Debug script to check name mapping logic"""

import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from jira_integration import JiraTempoAnalyzer

def debug_mapping_logic():
    """Debug the name mapping logic specifically"""
    print("🔍 Debugging Name Mapping Logic...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # Initialize analyzer
    analyzer = JiraTempoAnalyzer(config)
    
    target_date = datetime.strptime('2025-08-15', '%Y-%m-%d').date()
    
    print(f"📅 Target Date: {target_date}")
    
    # Get raw tempo data
    tempo_data = analyzer.tempo.get_user_worklogs(target_date, target_date)
    print(f"📊 Raw Tempo Data: {len(tempo_data)} users")
    
    for user, logs in tempo_data.items():
        total_hours = sum(log['time_spent_hours'] for log in logs)
        print(f"  👤 {user}: {total_hours:.1f}h")
    
    # Test the mapping function
    print(f"\n🔄 Testing Mapping Function...")
    print("=" * 40)
    
    for tempo_user in tempo_data.keys():
        mapped_name = analyzer._map_tempo_user_to_developer(tempo_user)
        print(f"'{tempo_user}' → '{mapped_name}'")
    
    # Test aggregation
    print(f"\n📈 Testing Aggregation...")
    print("=" * 30)
    
    aggregated_tempo = analyzer.tempo.aggregate_time_by_developer(tempo_data)
    print(f"Aggregated Data: {len(aggregated_tempo)} users")
    
    for user, data in aggregated_tempo.items():
        hours = data.get('total_hours_logged', 0)
        print(f"  👤 {user}: {hours:.1f}h")
    
    # Test the full mapping process
    print(f"\n🎯 Testing Full Mapping Process...")
    print("=" * 40)
    
    # Apply Tempo user mapping to aggregated data
    mapped_tempo_data = {}
    for tempo_user, data in aggregated_tempo.items():
        mapped_name = analyzer._map_tempo_user_to_developer(tempo_user)
        print(f"Mapping: '{tempo_user}' → '{mapped_name}'")
        
        # Merge data if developer already exists
        if mapped_name in mapped_tempo_data:
            existing_data = mapped_tempo_data[mapped_name]
            print(f"  Merging with existing data for {mapped_name}")
        else:
            mapped_tempo_data[mapped_name] = data
    
    print(f"\nFinal Mapped Data: {len(mapped_tempo_data)} users")
    for user, data in mapped_tempo_data.items():
        hours = data.get('total_hours_logged', 0)
        print(f"  ✅ {user}: {hours:.1f}h")

if __name__ == "__main__":
    debug_mapping_logic()