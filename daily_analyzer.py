#!/usr/bin/env python3
"""
Daily Developer Performance Analyzer
Main application that orchestrates multi-project analysis, AI insights, and Slack reporting
"""

import json
import logging
import os
import re
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

from dotenv import load_dotenv

from ai_router import AIRouter
from git_analyzer import MultiProjectGitAnalyzer
from slack_notifier import SlackNotifier
from jira_integration import JiraTempoAnalyzer


class DailyAnalyzer:
    """Main daily analyzer that coordinates all components"""
    
    def __init__(self, config_path: str = "config.json"):
        # Load environment variables
        load_dotenv()
        
        self.config_path = config_path
        self.config = self._load_config()
        self._setup_logging()
        
        # Initialize components
        self.git_analyzer = MultiProjectGitAnalyzer(self.config)
        self.ai_router = AIRouter(self.config)
        self.slack_notifier = SlackNotifier(self.config)
        self.jira_tempo_analyzer = JiraTempoAnalyzer(self.config)
        
        # Analysis settings
        self.report_config = self.config.get('report', {})
        self.analyze_days = self.report_config.get('analyze_days', 1)
        self.default_days_back = self.report_config.get('default_days_back', 1)
        self.combine_developers = self.report_config.get('combine_developers', True)
        self.save_local_reports = self.report_config.get('save_local_reports', True)
        self.reports_directory = Path(self.report_config.get('reports_directory', './reports'))
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file and substitute environment variables"""
        try:
            with open(self.config_path, 'r') as f:
                config_content = f.read()
            
            # Substitute environment variables
            config_content = self._substitute_env_vars(config_content)
            
            return json.loads(config_content)
        except FileNotFoundError:
            print(f"Configuration file {self.config_path} not found")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"Invalid JSON in configuration file: {e}")
            sys.exit(1)
    
    def _substitute_env_vars(self, content: str) -> str:
        """Substitute ${ENV_VAR} patterns with environment variable values"""
        def replace_env_var(match):
            env_var = match.group(1)
            value = os.getenv(env_var)
            if value is None:
                raise ValueError(f"Environment variable {env_var} is not set")
            return value
        
        return re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = getattr(logging, log_config.get('level', 'INFO').upper())
        log_file = log_config.get('file', './logs/dev_analyzer.log')
        
        # Create logs directory if it doesn't exist
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Daily Analyzer initialized")
    
    def get_analysis_date(self, days_back: int = None) -> datetime.date:
        """Get the date to analyze (default: yesterday)"""
        if days_back is None:
            days_back = self.default_days_back

        return (datetime.now() - timedelta(days=days_back)).date()
    
    def ensure_repositories(self) -> bool:
        """Ensure all repositories are available and up to date"""
        self.logger.info("Ensuring all repositories are up to date...")
        return self.git_analyzer.ensure_all_repositories()
    
    def analyze_date(self, target_date: datetime.date) -> Dict:
        """Perform complete analysis for a specific date"""
        date_str = target_date.strftime('%Y-%m-%d')
        self.logger.info(f"Starting analysis for {date_str}")
        
        try:
            # Get commits with detailed changes
            commits = self.git_analyzer.get_commits_with_changes(target_date)
            self.logger.info(f"Found {len(commits)} commits for {date_str}")
            
            if not commits:
                self.logger.info(f"No commits found for {date_str}")
                return {
                    'date': date_str,
                    'commits': [],
                    'developer_activity': {},
                    'summary_stats': {},
                    'ai_analysis': f"No commits found for {date_str}."
                }
            
            # Aggregate developer activity
            developer_activity = self.git_analyzer.aggregate_developer_activity(commits)
            self.logger.info(f"Aggregated activity for {len(developer_activity)} developers")
            
            # Generate summary statistics
            summary_stats = self.git_analyzer.generate_summary_stats(commits)
            
            # Get Jira and Tempo analysis
            jira_tempo_data = {}
            try:
                self.logger.info("Analyzing Jira tickets and Tempo time logs...")
                jira_tempo_data = self.jira_tempo_analyzer.analyze_developer_tickets_and_time(
                    commits, target_date, target_date
                )
            except Exception as e:
                self.logger.warning(f"Jira/Tempo analysis failed: {str(e)}")
                jira_tempo_data = {}

            # Get AI analysis
            # Generating AI analysis (disabled via config.ai.enabled=false)
            ai_enabled = self.config.get('ai', {}).get('enabled', True)
            if ai_enabled:
                self.logger.info("Generating AI analysis...")
                ai_analysis, ai_success = self.ai_router.generate_analysis(
                    commits, date_str,
                    jira_tempo_data=jira_tempo_data,
                    developer_activity=developer_activity
                )
            else:
                self.logger.info("AI analysis disabled by config; skipping.")
                ai_analysis, ai_success = ("", True)

            return {
                'date': date_str,
                'commits': commits,
                'developer_activity': developer_activity,
                'summary_stats': summary_stats,
                'jira_tempo_data': jira_tempo_data,
                'ai_analysis': ai_analysis,
                'ai_success': ai_success
            }
            
        except Exception as e:
            self.logger.error(f"Error during analysis for {date_str}: {str(e)}")
            raise
    
    def save_report(self, analysis_result: Dict) -> Optional[str]:
        """Save analysis report to local file"""
        if not self.save_local_reports:
            return None
        
        try:
            # Create reports directory
            self.reports_directory.mkdir(parents=True, exist_ok=True)
            
            # Generate report content
            report_content = self._format_text_report(analysis_result)
            
            # Save to file
            filename = f"daily_report_{analysis_result['date']}.txt"
            filepath = self.reports_directory / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"Report saved to {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Error saving report: {str(e)}")
            return None
    
    def _format_text_report(self, analysis_result: Dict) -> str:
        """Format analysis result as a text report"""
        date_str = analysis_result['date']
        developer_activity = analysis_result['developer_activity']
        summary_stats = analysis_result['summary_stats']
        jira_tempo_data = analysis_result.get('jira_tempo_data', {})
        ai_analysis = analysis_result['ai_analysis']
        
        report = []
        report.append("=" * 80)
        report.append("DAILY DEVELOPER PERFORMANCE REPORT")
        report.append("=" * 80)
        report.append(f"Date: {date_str}")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Summary statistics
        report.append("SUMMARY STATISTICS:")
        report.append("-" * 40)
        report.append(f"Total commits: {summary_stats.get('total_commits', 0)}")
        report.append(f"Active developers: {summary_stats.get('total_developers', 0)}")
        report.append(f"Projects involved: {summary_stats.get('total_projects', 0)}")
        report.append(f"Files changed: {summary_stats.get('total_files_changed', 0)}")
        report.append(f"Lines added: {summary_stats.get('total_insertions', 0)}")
        report.append(f"Lines deleted: {summary_stats.get('total_deletions', 0)}")
        report.append(f"Net lines: {summary_stats.get('net_lines', 0)}")
        report.append("")
        
        # Developer activity
        if developer_activity:
            report.append("DEVELOPER ACTIVITY:")
            report.append("-" * 40)
            
            for author, activity in sorted(developer_activity.items(),
                                         key=lambda x: x[1]['total_commits'], reverse=True):
                projects_text = ", ".join(activity['projects'])
                report.append(f"Developer: {author}")
                report.append(f"  Commits: {activity['total_commits']}")
                report.append(f"  Projects: {projects_text}")
                report.append(f"  Files changed: {activity['total_files_changed']}")
                report.append(f"  Lines: +{activity['total_insertions']}/-{activity['total_deletions']}")

                # Add working time information
                working_time = activity.get('working_time_analysis', {})
                if working_time:
                    time_range = working_time.get('time_range', 'Unknown')
                    estimated_hours = working_time.get('estimated_hours', 0)
                    work_pattern = working_time.get('work_pattern', 'Unknown')
                    sessions = working_time.get('work_sessions', 1)

                    # Get logged time for comparison
                    logged_hours = 0
                    if author in jira_tempo_data:
                        tempo_info = jira_tempo_data[author].get('tempo_data', {})
                        logged_hours = tempo_info.get('total_hours_logged', 0)

                    if logged_hours > 0:
                        time_comparison = f" vs {logged_hours}h logged"
                        if abs(estimated_hours - logged_hours) > 2:
                            time_comparison += " (discrepancy!)"
                        report.append(f"  Working time: {time_range} (~{estimated_hours}h{time_comparison}, {sessions} sessions)")
                    else:
                        report.append(f"  Working time: {time_range} (~{estimated_hours}h, {sessions} sessions, {work_pattern})")

                # Add Jira and Tempo information
                if author in jira_tempo_data:
                    jt_data = jira_tempo_data[author]

                    # Jira tickets from commits
                    if jt_data.get('commit_tickets'):
                        tickets_text = ", ".join(jt_data['commit_tickets'])
                        report.append(f"  Jira tickets: {tickets_text}")

                    # Tempo time logging with per-ticket breakdown
                    tempo_info = jt_data.get('tempo_data', {})
                    if tempo_info:
                        hours_logged = tempo_info.get('total_hours_logged', 0)
                        tickets_count = tempo_info.get('tickets_count', 0)
                        report.append(f"  Time logged: {hours_logged} hours on {tickets_count} tickets")

                        # Show time breakdown with descriptions
                        time_per_ticket = tempo_info.get('time_per_ticket', {})
                        ticket_descriptions = tempo_info.get('ticket_descriptions', {})
                        general_descriptions = tempo_info.get('general_descriptions', [])

                        if time_per_ticket:
                            report.append(f"  Time breakdown:")
                            for ticket, hours in time_per_ticket.items():
                                descriptions = ticket_descriptions.get(ticket, [])
                                if descriptions:
                                    # Show all descriptions for this ticket
                                    desc_text = "; ".join(descriptions)
                                    report.append(f"    {ticket}: {hours}h - {desc_text}")
                                else:
                                    report.append(f"    {ticket}: {hours}h")
                        elif general_descriptions:
                            # Show general work descriptions
                            report.append(f"  General work logged:")
                            for desc in general_descriptions:
                                report.append(f"    - {desc}")

                report.append("")
        
        # AI Analysis
        report.append("=" * 80)
        ai_success = analysis_result.get('ai_success', False)
        if ai_success:
            report.append("AI PERFORMANCE ANALYSIS")
        else:
            report.append("AI PERFORMANCE ANALYSIS (FAILED)")
        report.append("=" * 80)
        report.append("")
        report.append(ai_analysis)
        if not ai_success:
            report.append("")
            report.append("Note: AI analysis failed. Report contains basic statistics only.")
        report.append("")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def send_slack_notification(self, analysis_result: Dict) -> bool:
        """Send analysis result to Slack based on configuration and AI success"""
        try:
            ai_success = analysis_result.get('ai_success', False)
            require_ai_success = self.slack_notifier.slack_config.get('require_ai_success', True)

            # Check if AI analysis is required for Slack notifications
            if require_ai_success and not ai_success:
                self.logger.warning("AI analysis failed and require_ai_success=true, skipping Slack notification")
                return False

            # Also check if there's actual developer activity to report
            developer_activity = analysis_result.get('developer_activity', {})
            if not developer_activity:
                self.logger.info("No developer activity to report, skipping Slack notification")
                return False

            # Respect AI disable flag for Slack formatting dependencies
            ai_enabled = self.config.get('ai', {}).get('enabled', True)
            ai_text = analysis_result['ai_analysis'] if ai_enabled else ""

            # Send main daily report
            main_report_success = self.slack_notifier.send_daily_report(
                analysis_result['developer_activity'],
                ai_text,
                analysis_result['date'],
                analysis_result.get('jira_tempo_data', {})
            )

            # Send separate hours summary table
            if main_report_success:
                self.send_hours_summary(analysis_result)

            return main_report_success
        except Exception as e:
            self.logger.error(f"Error sending Slack notification: {str(e)}")
            return False

    def send_hours_summary(self, analysis_result: Dict):
        """Send a separate message with hours summary table"""
        try:
            date_str = analysis_result['date']
            jira_tempo_data = analysis_result.get('jira_tempo_data', {})

            # Collect all time tracking data
            hours_data = []
            total_hours = 0

            # Extract hours from jira_tempo_data (this is the correct source)
            for developer_name, dev_analysis in jira_tempo_data.items():
                tempo_data = dev_analysis.get('tempo_data', {})

                dev_total_hours = tempo_data.get('total_hours_logged', 0)
                worklogs = tempo_data.get('worklogs', [])
                unique_tickets = tempo_data.get('unique_tickets', [])

                if dev_total_hours > 0:
                    hours_data.append({
                        'name': developer_name,
                        'hours': dev_total_hours,
                        'tickets': len(unique_tickets),
                        'worklogs': len(worklogs)
                    })
                    total_hours += dev_total_hours

            # Sort by hours descending
            hours_data.sort(key=lambda x: x['hours'], reverse=True)

            if not hours_data:
                # Send a message indicating no time was logged
                message = f"⏰ **Time Tracking Summary - {date_str}**\n\n❌ No time logged by any developer yesterday."
            else:
                # Create the hours summary table
                message = f"⏰ **Time Tracking Summary - {date_str}**\n\n"
                message += "```\n"
                message += "Developer               Hours    Tickets\n"
                message += "─" * 40 + "\n"

                for data in hours_data:
                    name = data['name'][:20].ljust(20)  # Truncate and pad name
                    hours = f"{data['hours']:.1f}h".rjust(7)
                    tickets = f"{data['tickets']}".rjust(7)
                    message += f"{name} {hours} {tickets}\n"

                message += "─" * 40 + "\n"
                total_hours_str = f"{total_hours:.1f}h".rjust(7)
                message += f"{'TOTAL'.ljust(20)} {total_hours_str}\n"
                message += "```\n"

                # Add summary stats
                active_devs = len(hours_data)
                avg_hours = total_hours / active_devs if active_devs > 0 else 0
                message += f"\n📊 **Summary**: {active_devs} developers logged {total_hours:.1f} hours total (avg: {avg_hours:.1f}h per dev)"

            # Send the message
            self.slack_notifier.send_message(message)
            self.logger.info("Successfully sent hours summary to Slack")

        except Exception as e:
            self.logger.error(f"Error sending hours summary: {str(e)}")
            # Don't fail the main process if hours summary fails
    
    def analyze_date_range(self, start_date: datetime.date, end_date: datetime.date) -> Dict:
        """Analyze a range of dates and aggregate the results"""
        self.logger.info(f"Analyzing date range: {start_date} to {end_date}")

        all_commits = []
        date_range = []
        current_date = start_date

        while current_date <= end_date:
            date_commits = self.git_analyzer.get_commits_with_changes(current_date)
            all_commits.extend(date_commits)
            date_range.append(current_date.strftime('%Y-%m-%d'))
            current_date += timedelta(days=1)

        if not all_commits:
            date_range_str = f"{start_date} to {end_date}"
            return {
                'date': date_range_str,
                'commits': [],
                'developer_activity': {},
                'summary_stats': {},
                'ai_analysis': f"No commits found for date range {date_range_str}.",
                'ai_success': True
            }

        # Aggregate developer activity across the date range
        developer_activity = self.git_analyzer.aggregate_developer_activity(all_commits)
        self.logger.info(f"Aggregated activity for {len(developer_activity)} developers across {len(date_range)} days")

        # Generate summary statistics
        summary_stats = self.git_analyzer.generate_summary_stats(all_commits)

        # Get AI analysis for the date range
        date_range_str = f"{start_date} to {end_date}"
        self.logger.info("Generating AI analysis for date range...")
        ai_analysis, ai_success = self.ai_router.generate_analysis(all_commits, date_range_str)

        return {
            'date': date_range_str,
            'commits': all_commits,
            'developer_activity': developer_activity,
            'summary_stats': summary_stats,
            'ai_analysis': ai_analysis,
            'ai_success': ai_success
        }

    def run_daily_analysis(self, target_date: Optional[datetime.date] = None) -> bool:
        """Run the complete daily analysis workflow"""
        if target_date is None:
            target_date = self.get_analysis_date()
        
        date_str = target_date.strftime('%Y-%m-%d')
        
        try:
            self.logger.info(f"Starting daily analysis workflow for {date_str}")
            
            # Ensure repositories are up to date
            if not self.ensure_repositories():
                self.logger.warning("Some repositories could not be updated, continuing with available data")
            
            # Perform analysis
            analysis_result = self.analyze_date(target_date)
            
            # Save local report
            if self.save_local_reports:
                self.save_report(analysis_result)
            
            # Send Slack notification based on configuration
            if self.slack_notifier.is_configured():
                slack_success = self.send_slack_notification(analysis_result)
                if slack_success:
                    self.logger.info("Successfully sent Slack notification")
                elif analysis_result.get('ai_success', False):
                    self.logger.warning("Failed to send Slack notification despite successful AI analysis")
                else:
                    require_ai = self.slack_notifier.slack_config.get('require_ai_success', True)
                    if require_ai:
                        self.logger.info("Skipped Slack notification due to AI analysis failure (require_ai_success=true)")
                    else:
                        self.logger.warning("Failed to send Slack notification")
            else:
                self.logger.info("Slack not configured, skipping notification")
            
            self.logger.info(f"Daily analysis completed successfully for {date_str}")
            return True
            
        except Exception as e:
            error_msg = f"Daily analysis failed for {date_str}: {str(e)}"
            self.logger.error(error_msg)
            
            # Try to send error notification to Slack
            if self.slack_notifier.is_configured():
                self.slack_notifier.send_error_notification(error_msg)
            
            return False


def main():
    """Main entry point"""
    import argparse

    parser = argparse.ArgumentParser(description='Daily Developer Performance Analyzer')
    parser.add_argument('--config', default='config.json', help='Configuration file path')
    parser.add_argument('--date', help='Specific date to analyze (YYYY-MM-DD)')
    parser.add_argument('--days-back', type=int, help='Number of days back to analyze (default: 1 = yesterday)')
    parser.add_argument('--date-range', help='Date range to analyze (YYYY-MM-DD:YYYY-MM-DD)')
    parser.add_argument('--test-slack', action='store_true', help='Test Slack connection')

    args = parser.parse_args()
    
    # Initialize analyzer
    analyzer = DailyAnalyzer(args.config)
    
    # Test Slack connection if requested
    if args.test_slack:
        if analyzer.slack_notifier.test_connection():
            print("✅ Slack connection test successful")
            return 0
        else:
            print("❌ Slack connection test failed")
            return 1
    
    # Determine analysis type and dates
    if args.date_range:
        # Handle date range
        try:
            start_str, end_str = args.date_range.split(':')
            start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_str, '%Y-%m-%d').date()

            if start_date > end_date:
                print("Start date must be before or equal to end date")
                return 1

            # Ensure repositories are up to date
            if not analyzer.ensure_repositories():
                print("Warning: Some repositories could not be updated")

            # Analyze date range
            analysis_result = analyzer.analyze_date_range(start_date, end_date)

            # Save report
            if analyzer.save_local_reports:
                analyzer.save_report(analysis_result)

            # Send Slack notification
            if analyzer.slack_notifier.is_configured():
                slack_success = analyzer.send_slack_notification(analysis_result)
                if slack_success:
                    print("✅ Successfully sent Slack notification")
                else:
                    print("⚠️ Failed to send Slack notification")

            print(f"✅ Analysis completed for date range: {start_date} to {end_date}")
            return 0

        except ValueError:
            print("Invalid date range format. Use YYYY-MM-DD:YYYY-MM-DD")
            return 1
    else:
        # Handle single date analysis
        target_date = None
        if args.date:
            try:
                target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            except ValueError:
                print("Invalid date format. Use YYYY-MM-DD")
                return 1
        elif args.days_back:
            target_date = analyzer.get_analysis_date(args.days_back)

        # Run single date analysis
        success = analyzer.run_daily_analysis(target_date)
        return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
