#!/usr/bin/env python3
"""Debug script to check Tempo instance initialization"""

import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from jira_integration import TempoIntegration

def debug_tempo_instances():
    """Debug which Tempo instances are being initialized"""
    print("🔍 Debugging Tempo Instance Initialization...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # Initialize TempoIntegration
    tempo = TempoIntegration(config)
    
    print(f"✅ Tempo enabled: {tempo.enabled}")
    print(f"📊 Total instances configured: {len(tempo.tempo_keys)}")
    
    print("\n🏢 Tempo Instances:")
    print("=" * 50)
    
    for instance_name, instance_config in tempo.tempo_keys.items():
        api_key = instance_config['api_key']
        repositories = instance_config.get('repositories', [])
        
        print(f"Instance: {instance_name}")
        print(f"  API Key: {api_key[:10]}...{api_key[-10:] if len(api_key) > 20 else api_key}")
        print(f"  Repositories: {len(repositories)}")
        for repo in repositories:
            print(f"    - {repo}")
        print()
    
    print("\n🔧 Environment Variables Check:")
    print("=" * 40)
    env_vars = ['TEMPO_API_KEY', 'TEMPO_CONVX_API_KEY', 'TEMPO_TOPPROPERTY_API_KEY']
    for var in env_vars:
        value = os.getenv(var, '')
        if value:
            print(f"✅ {var}: {value[:10]}...{value[-10:] if len(value) > 20 else value}")
        else:
            print(f"❌ {var}: Not set")
    
    print("\n📋 Jira Instances in Config:")
    print("=" * 30)
    jira_config = config.get('jira', {})
    if 'instances' in jira_config:
        for instance in jira_config['instances']:
            name = instance.get('name', 'Unknown')
            tempo_key_env = instance.get('tempo_api_key', '').replace('${', '').replace('}', '')
            print(f"Instance: {name}")
            print(f"  Tempo Key Env: {tempo_key_env}")
            if tempo_key_env:
                actual_key = os.getenv(tempo_key_env, '')
                if actual_key:
                    print(f"  Key Value: {actual_key[:10]}...{actual_key[-10:] if len(actual_key) > 20 else actual_key}")
                else:
                    print(f"  Key Value: ❌ NOT SET")
            print()

if __name__ == "__main__":
    debug_tempo_instances()