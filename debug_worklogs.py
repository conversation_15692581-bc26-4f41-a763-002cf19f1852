#!/usr/bin/env python3
"""
Debug script to see the raw worklog format
"""

from jira_tempo_tracker import JiraTempoTracker
from datetime import datetime, timedelta
import json

def main():
    try:
        tracker = JiraTempoTracker()
        
        # Test connections
        jira_ok = tracker.test_jira_connection()
        tempo_ok = tracker.test_tempo_connection()
        
        if not (jira_ok and tempo_ok):
            print("❌ Connection failed")
            return
        
        print("✅ Connections successful!")
        
        # Get a few worklogs to see format
        from_date = datetime.now() - timedelta(days=7)
        to_date = datetime.now()
        
        worklogs_data = tracker.get_all_worklogs(from_date=from_date, to_date=to_date)
        
        if worklogs_data and worklogs_data.get('results'):
            print(f"\n📊 Found {len(worklogs_data['results'])} worklogs")
            print("\n🔍 Sample worklog structure:")
            
            # Show first worklog structure
            sample = worklogs_data['results'][0]
            print(json.dumps(sample, indent=2, default=str))
            
            # Show author info from all logs
            print("\n👥 All authors found:")
            authors = set()
            for log in worklogs_data['results'][:10]:  # First 10
                author = log.get('author', {})
                author_info = f"Name: {author.get('displayName', 'N/A')}, Email: {author.get('emailAddress', 'N/A')}, AccountId: {author.get('accountId', 'N/A')}"
                authors.add(author_info)
            
            for i, author in enumerate(authors, 1):
                print(f"  {i}. {author}")
        else:
            print("❌ No worklogs found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()