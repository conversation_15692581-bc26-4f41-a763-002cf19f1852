{"ai": {"provider": "openrouter", "openrouter": {"api_key": "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", "base_url": "https://openrouter.ai/api/v1", "model": "meta-llama/llama-3.2-3b-instruct:free", "max_tokens": 1500, "temperature": 0.7}, "openai": {"api_key": "", "model": "gpt-4", "max_tokens": 1500, "temperature": 0.7}}, "slack": {"token": "*********************************************************", "channel_id": "C09ANDRBK7C", "enabled": true, "require_ai_success": true}, "developer_mapping": {"global": {"ivanDAleannlab": "<PERSON>", "IvanD": "<PERSON>", "dhaidamachenko1": "<PERSON><PERSON><PERSON>", "maxims": "<PERSON>", "Oleksii": "<PERSON><PERSON><PERSON>", "comalex": "<PERSON><PERSON><PERSON>", "Oleksii Stupak": "<PERSON><PERSON><PERSON>", "john.doe": "<PERSON>", "j.doe": "<PERSON>", "jane.smith": "<PERSON>", "alice.dev": "<PERSON>", "bob.coder": "<PERSON>"}}, "report": {"schedule": "09:00", "timezone": "UTC", "analyze_days": 1, "combine_developers": true, "save_local_reports": true, "reports_directory": "./reports"}, "projects": [{"name": "Childfree Legacy", "repository_url": "**************:ChildfreeLegacyOrg/Childfree_Legacy.git", "local_path": "./repositories/childfree_legacy", "enabled": true}, {"name": "Frontend App", "repository_url": "**************:company/frontend-app.git", "local_path": "./repositories/frontend_app", "enabled": true}, {"name": "Backend API", "repository_url": "**************:company/backend-api.git", "local_path": "./repositories/backend_api", "enabled": true}, {"name": "Mobile App", "repository_url": "**************:company/mobile-app.git", "local_path": "./repositories/mobile_app", "enabled": false}], "logging": {"level": "INFO", "file": "./logs/dev_analyzer.log", "max_file_size": "10MB", "backup_count": 5}}