#!/usr/bin/env python3
"""
Daily Developer Performance Analyzer
Analyzes git commits from yesterday and uses OpenRouter AI to provide insights
"""

import subprocess
import json
import requests
from datetime import datetime, timedelta
import os
import sys
from typing import Dict, List, Tuple, Optional

# OpenRouter API configuration
OPENROUTER_API_KEY = "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"
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

class DailyPerformanceAnalyzer:
    def __init__(self):
        self.yesterday = (datetime.now() - timedelta(days=1)).date()
        self.yesterday_str = self.yesterday.strftime('%Y-%m-%d')
        
    def get_yesterday_commits(self) -> List[Dict]:
        """Get all commits from yesterday with detailed information"""
        try:
            # Get commits from yesterday with full details
            result = subprocess.run([
                'git', 'log', 
                '--since', f'{self.yesterday_str} 00:00:00',
                '--until', f'{self.yesterday_str} 23:59:59',
                '--pretty=format:%H|%an|%ae|%ad|%s',
                '--date=iso'
            ], capture_output=True, text=True, check=True)
            
            commits = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split('|', 4)
                    if len(parts) == 5:
                        commits.append({
                            'hash': parts[0],
                            'author': parts[1],
                            'email': parts[2],
                            'date': parts[3],
                            'message': parts[4]
                        })
            return commits
        except subprocess.CalledProcessError as e:
            print(f"Error getting git commits: {e}")
            return []

    def get_commit_changes(self, commit_hash: str) -> Dict:
        """Get detailed changes for a specific commit"""
        try:
            # Get file changes statistics
            result = subprocess.run([
                'git', 'show', '--stat', '--format=', commit_hash
            ], capture_output=True, text=True, check=True)
            
            # Get actual diff
            diff_result = subprocess.run([
                'git', 'show', '--format=', commit_hash
            ], capture_output=True, text=True, check=True)
            
            # Parse statistics
            stats = {'files_changed': 0, 'insertions': 0, 'deletions': 0, 'files': []}
            lines = result.stdout.strip().split('\n')
            
            for line in lines:
                if '|' in line and ('+' in line or '-' in line):
                    parts = line.split('|')
                    if len(parts) >= 2:
                        filename = parts[0].strip()
                        changes = parts[1].strip()
                        stats['files'].append(filename)
                        stats['files_changed'] += 1
                        
                        # Count insertions and deletions
                        plus_count = changes.count('+')
                        minus_count = changes.count('-')
                        stats['insertions'] += plus_count
                        stats['deletions'] += minus_count
            
            return {
                'stats': stats,
                'diff': diff_result.stdout[:2000]  # Limit diff size for AI analysis
            }
        except subprocess.CalledProcessError as e:
            print(f"Error getting commit changes for {commit_hash}: {e}")
            return {'stats': {'files_changed': 0, 'insertions': 0, 'deletions': 0, 'files': []}, 'diff': ''}

    def analyze_with_ai(self, commits_data: List[Dict]) -> str:
        """Use OpenRouter AI to analyze the commits and provide insights"""
        if not commits_data:
            return "No commits found for yesterday."
        
        # Prepare data for AI analysis
        analysis_prompt = f"""
Analyze the following git commits from {self.yesterday_str} and provide a comprehensive developer performance summary:

COMMITS DATA:
"""
        
        for i, commit in enumerate(commits_data, 1):
            analysis_prompt += f"""
Commit {i}:
- Author: {commit['author']}
- Time: {commit['date']}
- Message: {commit['message']}
- Files changed: {commit['changes']['stats']['files_changed']}
- Lines added: {commit['changes']['stats']['insertions']}
- Lines deleted: {commit['changes']['stats']['deletions']}
- Files: {', '.join(commit['changes']['stats']['files'][:5])}{'...' if len(commit['changes']['stats']['files']) > 5 else ''}

"""

        analysis_prompt += """
Please provide a detailed analysis including:
1. **Summary of Work Done**: What was accomplished yesterday?
2. **Code Quality Assessment**: Based on commit messages and changes, how would you rate the quality?
3. **Productivity Metrics**: Number of commits, lines changed, files touched
4. **Work Patterns**: Time distribution, commit frequency
5. **Areas of Focus**: What parts of the codebase were worked on?
6. **Recommendations**: Suggestions for improvement or areas to focus on

Format the response in a clear, professional manner suitable for a daily performance report.
"""

        try:
            response = requests.post(
                f"{OPENROUTER_BASE_URL}/chat/completions",
                headers={
                    "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "meta-llama/llama-3.1-8b-instruct:free",  # Free model
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a senior software engineering manager analyzing developer performance based on git commit data. Provide constructive, detailed insights."
                        },
                        {
                            "role": "user",
                            "content": analysis_prompt
                        }
                    ],
                    "max_tokens": 1500,
                    "temperature": 0.7
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"Error from OpenRouter API: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error calling OpenRouter API: {str(e)}"

    def generate_performance_report(self) -> str:
        """Generate the complete daily performance report"""
        print(f"Analyzing commits for {self.yesterday_str}...")
        
        # Get yesterday's commits
        commits = self.get_yesterday_commits()
        
        if not commits:
            return f"No commits found for {self.yesterday_str}"
        
        # Get detailed changes for each commit
        commits_with_changes = []
        for commit in commits:
            changes = self.get_commit_changes(commit['hash'])
            commit['changes'] = changes
            commits_with_changes.append(commit)
        
        # Generate basic statistics
        total_commits = len(commits)
        total_files = sum(c['changes']['stats']['files_changed'] for c in commits_with_changes)
        total_insertions = sum(c['changes']['stats']['insertions'] for c in commits_with_changes)
        total_deletions = sum(c['changes']['stats']['deletions'] for c in commits_with_changes)
        
        # Get unique authors
        authors = list(set(c['author'] for c in commits))
        
        # Create report header
        report = f"""
{'='*80}
DAILY DEVELOPER PERFORMANCE REPORT
{'='*80}
Date: {self.yesterday_str}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

QUICK STATISTICS:
- Total commits: {total_commits}
- Developers active: {len(authors)}
- Files changed: {total_files}
- Lines added: {total_insertions}
- Lines deleted: {total_deletions}
- Net lines: {total_insertions - total_deletions}

ACTIVE DEVELOPERS:
{', '.join(authors)}

COMMIT DETAILS:
"""
        
        for i, commit in enumerate(commits_with_changes, 1):
            report += f"""
Commit {i}: {commit['hash'][:8]}
Author: {commit['author']}
Time: {commit['date']}
Message: {commit['message']}
Changes: {commit['changes']['stats']['files_changed']} files, +{commit['changes']['stats']['insertions']}/-{commit['changes']['stats']['deletions']} lines
"""
        
        # Get AI analysis
        print("Getting AI analysis...")
        ai_analysis = self.analyze_with_ai(commits_with_changes)
        
        report += f"""

{'='*80}
AI PERFORMANCE ANALYSIS
{'='*80}

{ai_analysis}

{'='*80}
"""
        
        return report

    def save_report(self, report: str) -> str:
        """Save the report to a file"""
        filename = f"daily_performance_report_{self.yesterday_str}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            return filename
        except Exception as e:
            print(f"Error saving report: {e}")
            return None

def main():
    """Main function to run the daily performance analysis"""
    analyzer = DailyPerformanceAnalyzer()
    
    print("Starting daily performance analysis...")
    report = analyzer.generate_performance_report()
    
    # Save report
    filename = analyzer.save_report(report)
    if filename:
        print(f"\nReport saved to: {filename}")
    
    # Display report
    print("\n" + "="*50)
    print("DAILY PERFORMANCE REPORT PREVIEW")
    print("="*50)
    print(report)

if __name__ == "__main__":
    main()
