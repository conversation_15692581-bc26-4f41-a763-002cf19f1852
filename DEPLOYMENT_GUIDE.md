# Git Analyzer - Server Deployment Guide 🚀

## ✅ **Deployment Complete!**

The Git Analyzer has been successfully deployed to your server at `***************` with automated daily reports and GitHub Actions auto-deployment.

## 📋 **What's Been Set Up**

### 🖥️ **Server Configuration**
- **Server**: `root@***************`
- **Project Path**: `/root/git-analyzer`
- **Python**: 3.11.6 with pip installed
- **Dependencies**: `requests`, `python-dotenv`
- **Directories**: `reports/`, `logs/`

### ⏰ **Automated Daily Reports**
- **Schedule**: Every day at **8:50 AM Poland time** (6:50 AM UTC)
- **Cron Job**: `50 6 * * * cd /root/git-analyzer && python3 daily_analyzer.py >> /root/git-analyzer/logs/daily_analyzer.log 2>&1`
- **Logs**: Stored in `/root/git-analyzer/logs/daily_analyzer.log`

### 🔄 **Auto-Deployment with GitHub Actions**
- **Trigger**: Automatic deployment on every push to `main` branch
- **Workflow**: `.github/workflows/deploy.yml`
- **Manual Trigger**: Available via GitHub Actions UI

## 🔧 **Required Setup Steps**

### 1. **Environment Variables on Server**
You need to update the `.env` file on the server with your actual API keys:

```bash
ssh root@***************
cd /root/git-analyzer
nano .env
```

Update with your real values:
```env
# OpenRouter API Key for AI analysis
OPENROUTER_API_KEY=your_actual_openrouter_key

# Slack Bot Token for notifications  
SLACK_TOKEN=your_actual_slack_token

# Jira API credentials
JIRA_API_KEY=your_actual_jira_key
JIRA_EMAIL=your_actual_jira_email

# Tempo API Key for time tracking
TEMPO_API_KEY=your_actual_tempo_key
```

### 2. **GitHub Secrets for Auto-Deployment**
Add these secrets to your GitHub repository (`Settings > Secrets and variables > Actions`):

- **`SERVER_HOST`**: `***************`
- **`SERVER_USER`**: `root`
- **`SERVER_SSH_KEY`**: Your private SSH key content

To get your SSH private key:
```bash
cat ~/.ssh/id_rsa
```

### 3. **Test the Setup**
Once environment variables are set, test manually:
```bash
ssh root@***************
cd /root/git-analyzer
python3 daily_analyzer.py --date 2025-08-15
```

## 📁 **Server File Structure**
```
/root/git-analyzer/
├── daily_analyzer.py          # Main analysis script
├── jira_integration.py        # Enhanced Jira/Tempo integration
├── slack_notifier.py          # Slack notifications
├── config.json               # Configuration file
├── .env                      # Environment variables (needs your keys)
├── deploy.sh                 # Deployment script
├── reports/                  # Generated reports
├── logs/                     # Application logs
└── ...                       # Other project files
```

## 🔄 **How Auto-Deployment Works**

1. **Push to GitHub**: When you push changes to the `main` branch
2. **GitHub Actions Triggers**: The workflow automatically starts
3. **SSH to Server**: Connects to your server using stored SSH key
4. **Run Deployment**: Executes `deploy.sh` which:
   - Pulls latest code from GitHub
   - Installs/updates dependencies
   - Sets proper permissions
   - Creates necessary directories

## 📊 **Daily Report Schedule**

**Poland Time**: 8:50 AM every day
**UTC Time**: 6:50 AM every day (server time)

The system will:
1. Analyze yesterday's commits automatically
2. Generate comprehensive reports with Jira/Tempo integration
3. Send Slack notifications (if configured)
4. Save reports to `/root/git-analyzer/reports/`
5. Log all activity to `/root/git-analyzer/logs/`

## 🛠️ **Manual Operations**

### Run Deployment Manually
```bash
ssh root@***************
cd /root/git-analyzer
./deploy.sh
```

### Check Cron Job Status
```bash
ssh root@***************
crontab -l | grep git-analyzer
```

### View Logs
```bash
ssh root@***************
tail -f /root/git-analyzer/logs/daily_analyzer.log
```

### Test Analysis for Specific Date
```bash
ssh root@***************
cd /root/git-analyzer
python3 daily_analyzer.py --date 2025-08-15
```

## 🚨 **Troubleshooting**

### If Daily Reports Don't Work
1. Check environment variables are set correctly
2. Verify cron job is installed: `crontab -l`
3. Check logs: `tail /root/git-analyzer/logs/daily_analyzer.log`
4. Test manually: `python3 daily_analyzer.py --date $(date -d yesterday +%Y-%m-%d)`

### If Auto-Deployment Fails
1. Check GitHub Secrets are set correctly
2. Verify SSH key has proper permissions
3. Check GitHub Actions logs in repository
4. Test SSH connection manually: `ssh root@***************`

## 🎯 **Next Steps**

1. **Set Environment Variables**: Update `.env` with your actual API keys
2. **Configure GitHub Secrets**: Add SSH key and server details
3. **Test Push**: Make a small change and push to verify auto-deployment
4. **Monitor First Run**: Check tomorrow at 8:50 AM Poland time for first automated report

## 🎉 **Features Now Available**

- ✅ **Daily automated reports** at 8:50 AM Poland time
- ✅ **Enhanced Jira/Tempo integration** with real user names and worklog descriptions
- ✅ **Auto-deployment** on every code push
- ✅ **Complete server setup** with all dependencies
- ✅ **Comprehensive logging** for monitoring and debugging
- ✅ **Manual deployment script** for quick updates

Your Git Analyzer is now fully deployed and ready for automated daily developer productivity reports! 🚀
