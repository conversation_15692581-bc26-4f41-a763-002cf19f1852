#!/usr/bin/env python3
"""
Debug script to find all Tempo user names and help create proper mappings
"""

import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from jira_integration import JiraTempoAnalyzer

def debug_tempo_users():
    """Debug Tempo users to understand naming patterns"""
    print("🔍 Debugging Tempo User Names...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    analyzer = JiraTempoAnalyzer(config)
    
    if not analyzer.tempo.is_configured():
        print("❌ Tempo not configured")
        return
    
    # Get worklogs for the last week to see all users
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=7)
    
    print(f"📅 Checking worklogs from {start_date} to {end_date}")
    
    # Get raw worklogs
    raw_worklogs = analyzer.tempo.get_user_worklogs(start_date, end_date)
    
    print(f"\n👥 Found {len(raw_worklogs)} unique users in Tempo:")
    print("=" * 60)
    
    for user_name, worklogs in raw_worklogs.items():
        total_hours = sum(log['time_spent_hours'] for log in worklogs)
        unique_tickets = set(log['issue_key'] for log in worklogs if log['issue_key'])
        
        print(f"🧑‍💻 {user_name}")
        print(f"   Total Hours: {total_hours:.1f}h")
        print(f"   Tickets: {len(unique_tickets)} unique")
        print(f"   Tempo Instance: {worklogs[0].get('tempo_instance', 'Unknown') if worklogs else 'Unknown'}")
        
        # Show sample worklog for debugging
        if worklogs:
            sample = worklogs[0]
            print(f"   Sample: {sample.get('description', 'No description')[:50]}...")
            print(f"   Email: {sample.get('author_email', 'No email')}")
        print()
    
    # Check current mapping
    print("\n🗂️ Current Tempo Mapping:")
    print("=" * 40)
    tempo_mapping = config.get('developer_mapping', {}).get('tempo', {})
    for tempo_name, mapped_name in tempo_mapping.items():
        print(f"'{tempo_name}' → '{mapped_name}'")
    
    # Check global mapping
    print("\n🌍 Current Global Mapping:")
    print("=" * 40)
    global_mapping = config.get('developer_mapping', {}).get('global', {})
    for git_name, mapped_name in global_mapping.items():
        print(f"'{git_name}' → '{mapped_name}'")
    
    # Suggest missing mappings
    print("\n💡 Suggested Tempo Mappings to Add:")
    print("=" * 50)
    
    mapped_users = set(tempo_mapping.values())
    all_tempo_users = set(raw_worklogs.keys())
    
    for tempo_user in all_tempo_users:
        # Try to find a match in global mapping
        potential_match = None
        for mapped_name in global_mapping.values():
            if mapped_name not in mapped_users and tempo_user not in tempo_mapping:
                # Simple heuristic matching
                if (mapped_name.split()[0].lower() in tempo_user.lower() or 
                    tempo_user.split()[0].lower() in mapped_name.lower()):
                    potential_match = mapped_name
                    break
        
        if tempo_user not in tempo_mapping:
            if potential_match:
                print(f"'{tempo_user}' → '{potential_match}' (suggested)")
            else:
                print(f"'{tempo_user}' → '???' (needs manual mapping)")

def suggest_complete_mapping():
    """Suggest a complete mapping based on common patterns"""
    print("\n🔧 Suggested Complete Tempo Mapping:")
    print("=" * 50)
    
    # Common mappings based on typical patterns
    suggested_mappings = {
        # Existing mappings (keep these)
        "712020:793f4892-002c-41b1-a76e-3c49cc7fe9a0": "Oleksii Stupak",
        "User-cc7fe9a0": "Oleksii Stupak",
        "User-8cf57eb6": "Ivan D",
        "Dmytro Haidamachenko": "Dmytro Haidamachenko",
        "Ivan Derkachov": "Ivan D",
        "Rostyslav Fedorenko": "Rostyslav F",
        
        # Additional likely mappings
        "Oleksii Stupak": "Oleksii Stupak",
        "Ivan D": "Ivan D",
        "Dmytro Marjan": "Dmytro Marjan",
        "Dmytro Perepelytsa": "Dmytro Perepelytsa",
        "Maxim Sidorenko": "Maxim Sidorenko",
        "Rostyslav F": "Rostyslav F",
        "rostyslaf": "Rostyslav F",
        "Rostyslav": "Rostyslav F",
        
        # Account ID patterns (add more as discovered)
        "User-8cf57eb6": "Ivan D",
        "User-cc7fe9a0": "Oleksii Stupak",
    }
    
    print('    "tempo": {')
    for tempo_name, mapped_name in suggested_mappings.items():
        print(f'      "{tempo_name}": "{mapped_name}",')
    print('    }')

def main():
    """Run the debug analysis"""
    print("🧪 Tempo User Mapping Debug Tool")
    print("=" * 60)
    
    try:
        debug_tempo_users()
        suggest_complete_mapping()
        
        print("\n📋 Next Steps:")
        print("1. Review the Tempo user names above")
        print("2. Update config.json with missing mappings")
        print("3. Test with: python3 daily_analyzer.py --date 2025-08-15")
        print("4. Verify all developers show 'Time Tracked' field")
        
    except Exception as e:
        print(f"\n❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
