#!/bin/bash

echo "🚀 Complete User Mapping & Slack Hours Fix"
echo "=========================================="

# Check if .env file exists
if [ -f .env ]; then
    echo "✅ Found .env file, loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "⚠️ No .env file found. Make sure environment variables are set."
fi

# Check required environment variables
required_vars=(
    "TEMPO_ALEANNLAB_API_KEY"
    "JIRA_ALEANNLAB_API_KEY" 
    "JIRA_ALEANNLAB_EMAIL"
    "SLACK_TOKEN"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Missing required environment variables:"
    printf '   %s\n' "${missing_vars[@]}"
    echo ""
    echo "Please add them to your .env file:"
    echo "TEMPO_ALEANNLAB_API_KEY=your_tempo_key"
    echo "JIRA_ALEANNLAB_API_KEY=ATATT3xFfGF0EvE_d87QsesavsDdTiceSkANhr1iELtItSrEJ6os5wUpk46URlU8B4kqVyTgteap36098hfl1v4Ei3auMICf26TcZmKZtehe9MaBJTg7kBcL3Hdb4VKwzCR8QTxzMzlfHtJKq3ZPDokRELRVlniXrzrWN17kDCvAd_JIIXvOWGs=AA5FC415"
    echo "JIRA_ALEANNLAB_EMAIL=<EMAIL>"
    echo "SLACK_TOKEN=your_slack_token"
    exit 1
fi

echo "✅ All required environment variables are set"
echo ""

# Step 1: Run comprehensive user mapping
echo "📋 Step 1: Running comprehensive user mapping..."
echo "----------------------------------------------"
python3 comprehensive_user_mapping.py

if [ $? -eq 0 ]; then
    echo "✅ User mapping completed successfully"
else
    echo "❌ User mapping failed"
    exit 1
fi

echo ""

# Step 2: Test and fix Slack hours reporting
echo "🔧 Step 2: Testing Slack hours reporting..."
echo "-------------------------------------------"
python3 fix_slack_hours_reporting.py

if [ $? -eq 0 ]; then
    echo "✅ Slack hours reporting test completed"
else
    echo "❌ Slack hours reporting test failed"
    exit 1
fi

echo ""

# Step 3: Show current mappings
echo "📊 Step 3: Showing current mappings..."
echo "-------------------------------------"
python3 show_tempo_mappings.py

echo ""
echo "🎯 All steps completed!"
echo "======================"
echo ""
echo "Next steps:"
echo "1. Review the updated config.json file"
echo "2. Test the daily analyzer: python3 cron_daily_analysis.py --test"
echo "3. Check your Slack channel for the test report"
echo ""
echo "If everything looks good, your daily reports should now include:"
echo "• Proper user name mappings"
echo "• Time tracking hours in the main report"
echo "• Separate hours summary table"
