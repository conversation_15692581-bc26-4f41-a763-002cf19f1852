#!/usr/bin/env python3
"""
Test script to demonstrate Jira and Tempo API functionality
"""

from jira_tempo_tracker import JiraTempoTracker
from datetime import datetime, timedelta
import requests

def main():
    try:
        tracker = JiraTempoTracker()
        
        print("🔍 Testing API connections...")
        print()
        
        # Test connections
        jira_ok = tracker.test_jira_connection()
        tempo_ok = tracker.test_tempo_connection()
        
        if not (jira_ok and tempo_ok):
            print("\n❌ One or more connections failed. Please check your configuration.")
            return
        
        print("\n✅ All connections successful!")
        print("\n" + "="*60)
        
        # Get user account info first
        print(f"\n👤 Getting user account info...")
        url = f"{tracker.jira_base_url}/rest/api/3/myself"
        response = requests.get(
            url, 
            headers=tracker.jira_headers,
            auth=(tracker.jira_email, tracker.jira_api_key)
        )
        
        if response.status_code == 200:
            user_info = response.json()
            account_id = user_info.get('accountId')
            print(f"✅ Account ID: {account_id}")
            
            # Get user hours summary for last 7 days
            print(f"\n📊 Getting hours summary for {tracker.jira_email} (last 7 days)...")
            from_date = datetime.now() - timedelta(days=7)
            to_date = datetime.now()
            user_hours = tracker.get_user_hours_summary(
                account_id, 
                from_date=from_date,
                to_date=to_date
            )
        else:
            print(f"❌ Failed to get user info: {response.status_code}")
            user_hours = None
        
        if user_hours:
            print(f"✅ Your hours (last 7 days): {user_hours['total_hours']}h across {user_hours['worklog_count']} entries")
            
            # Show some worklog details if available
            if user_hours.get('worklogs'):
                print("\n📝 Recent worklogs:")
                for i, log in enumerate(user_hours['worklogs'][:5]):
                    hours = round(log.get('timeSpentSeconds', 0) / 3600, 2)
                    print(f"  {i+1}. {log.get('issue', {}).get('key', 'N/A')} - {hours}h on {log.get('startDate', 'N/A')}")
                    if log.get('description'):
                        desc = log['description'][:80] + ('...' if len(log['description']) > 80 else '')
                        print(f"     Description: {desc}")
        else:
            print("❌ Failed to get user hours summary")
        
        print("\n" + "="*60)
        print("📋 Script capabilities:")
        print("• Connect to Jira and Tempo APIs")
        print("• Get hours tracked for specific tickets")
        print("• Get user time tracking summaries")
        print("• Filter by date ranges")
        print("• Show detailed worklog information")
        print("\n💡 To test with a specific ticket, modify the script or use:")
        print("   tracker.get_issue_hours('CHIL-123')")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()