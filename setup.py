#!/usr/bin/env python3
"""
Setup script for Developer Performance Analyzer
Handles installation, configuration, and initial setup
"""

import os
import sys
import json
import subprocess
from pathlib import Path
import shutil


class SetupManager:
    """Manages the setup and installation process"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent.absolute()
        self.config_file = self.script_dir / "config.json"
    
    def check_python_version(self):
        """Check if Python version is compatible"""
        if sys.version_info < (3, 7):
            print("❌ Python 3.7 or higher is required")
            print(f"Current version: {sys.version}")
            return False
        print(f"✅ Python version: {sys.version}")
        return True
    
    def check_git_installation(self):
        """Check if Git is installed and accessible"""
        try:
            result = subprocess.run(['git', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Git version: {result.stdout.strip()}")
                return True
            else:
                print("❌ Git is not working properly")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Git is not installed or not in PATH")
            print("Please install Git: https://git-scm.com/downloads")
            return False
    
    def install_dependencies(self):
        """Install Python dependencies"""
        print("Installing Python dependencies...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], cwd=self.script_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Dependencies installed successfully")
                return True
            else:
                print(f"❌ Failed to install dependencies: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ Error installing dependencies: {e}")
            return False
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.script_dir / "logs",
            self.script_dir / "reports",
            self.script_dir / "repositories"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created directory: {directory}")
    
    def validate_config(self):
        """Validate the configuration file"""
        if not self.config_file.exists():
            print("❌ Configuration file config.json not found")
            return False
        
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)
            
            # Check required sections
            required_sections = ['ai', 'slack', 'report', 'projects']
            for section in required_sections:
                if section not in config:
                    print(f"❌ Missing required section in config: {section}")
                    return False
            
            # Check AI configuration
            ai_config = config['ai']
            provider = ai_config.get('provider', 'openrouter')
            if provider not in ai_config:
                print(f"❌ AI provider '{provider}' not configured")
                return False
            
            # Check if API key is set
            provider_config = ai_config[provider]
            if not provider_config.get('api_key'):
                print(f"❌ API key not set for {provider}")
                return False
            
            # Check Slack configuration
            slack_config = config['slack']
            if slack_config.get('enabled', True):
                if not slack_config.get('token'):
                    print("❌ Slack token not configured")
                    return False
                if not slack_config.get('channel_id'):
                    print("❌ Slack channel ID not configured")
                    return False
            
            # Check projects
            projects = config.get('projects', [])
            if not projects:
                print("❌ No projects configured")
                return False
            
            for project in projects:
                if not project.get('name'):
                    print("❌ Project missing name")
                    return False
                if not project.get('repository_url'):
                    print("❌ Project missing repository_url")
                    return False
                if not project.get('local_path'):
                    print("❌ Project missing local_path")
                    return False
            
            print("✅ Configuration file is valid")
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in config file: {e}")
            return False
        except Exception as e:
            print(f"❌ Error validating config: {e}")
            return False
    
    def test_components(self):
        """Test all components"""
        print("\nTesting components...")
        
        try:
            # Change to script directory
            os.chdir(self.script_dir)
            
            # Import and test components
            from daily_analyzer import DailyAnalyzer
            
            analyzer = DailyAnalyzer()
            
            # Test Slack connection
            print("Testing Slack connection...")
            if analyzer.slack_notifier.test_connection():
                print("✅ Slack connection successful")
            else:
                print("⚠️ Slack connection failed (check token and channel ID)")
            
            # Test AI router
            print("Testing AI router...")
            available_providers = analyzer.ai_router.list_available_providers()
            if available_providers:
                print(f"✅ AI providers available: {', '.join(available_providers)}")
            else:
                print("❌ No AI providers configured properly")
                return False
            
            # Test repository access
            print("Testing repository access...")
            if analyzer.ensure_repositories():
                print("✅ Repository access successful")
            else:
                print("⚠️ Some repositories could not be accessed")
            
            print("✅ Component testing completed")
            return True
            
        except Exception as e:
            print(f"❌ Component testing failed: {e}")
            return False
    
    def show_cron_instructions(self):
        """Show instructions for setting up the cron job"""
        print("\n" + "="*60)
        print("CRON JOB SETUP INSTRUCTIONS")
        print("="*60)
        print()
        print("To schedule the daily analysis to run at 9:00 AM every day:")
        print()
        print("1. Open your crontab:")
        print("   crontab -e")
        print()
        print("2. Add the following line:")
        print(f"   0 9 * * * cd {self.script_dir} && {sys.executable} cron_daily_analysis.py")
        print()
        print("3. Save and exit the editor")
        print()
        print("To verify the cron job is installed:")
        print("   crontab -l")
        print()
        print("To test the cron script manually:")
        print(f"   cd {self.script_dir}")
        print("   python3 cron_daily_analysis.py --test")
        print()
        print("To view cron logs:")
        print(f"   tail -f {self.script_dir}/logs/cron_daily_analysis.log")
    
    def run_setup(self):
        """Run the complete setup process"""
        print("Developer Performance Analyzer Setup")
        print("="*50)
        print()
        
        # Check prerequisites
        if not self.check_python_version():
            return False
        
        if not self.check_git_installation():
            return False
        
        # Install dependencies
        if not self.install_dependencies():
            return False
        
        # Create directories
        self.create_directories()
        
        # Validate configuration
        if not self.validate_config():
            print("\nPlease check and update your config.json file")
            return False
        
        # Test components
        if not self.test_components():
            print("\nSome components failed testing. Please check your configuration.")
            return False
        
        print("\n" + "="*50)
        print("✅ SETUP COMPLETED SUCCESSFULLY!")
        print("="*50)
        
        # Show next steps
        self.show_cron_instructions()
        
        return True


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Setup Developer Performance Analyzer')
    parser.add_argument('--test-only', action='store_true',
                       help='Only run component tests, skip installation')
    
    args = parser.parse_args()
    
    setup_manager = SetupManager()
    
    if args.test_only:
        print("Running component tests only...")
        success = setup_manager.test_components()
        return 0 if success else 1
    
    success = setup_manager.run_setup()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
