#!/usr/bin/env python3
"""
Jira and Tempo API connector to track hours logged on tickets.
"""

import os
import requests
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

class JiraTempoTracker:
    def __init__(self):
        self.jira_api_key = os.getenv('JIRA_API_KEY')
        self.jira_email = os.getenv('JIRA_EMAIL')
        self.tempo_api_key = os.getenv('TEMPO_API_KEY')
        
        if not all([self.jira_api_key, self.jira_email, self.tempo_api_key]):
            raise ValueError("Missing required environment variables: JIRA_API_KEY, JIRA_EMAIL, TEMPO_API_KEY")
        
        # Jira and Tempo base URLs from config
        self.jira_base_url = "https://childfreelegacy.atlassian.net"
        # Try different Tempo API versions
        tempo_versions = [
            "https://api.tempo.io/core/3",
            "https://api.tempo.io/4", 
            "https://api.tempo.io/core/4",
            "https://api.tempo.io/v3",
            "https://api.tempo.io/v4"
        ]
        self.tempo_base_url = "https://api.tempo.io/4"  # Use v4 which works
        
        self.jira_headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        }
        
        self.tempo_headers = {
            'Authorization': f'Bearer {self.tempo_api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    
    def test_jira_connection(self):
        """Test Jira API connection"""
        try:
            url = f"{self.jira_base_url}/rest/api/3/myself"
            response = requests.get(
                url, 
                headers=self.jira_headers,
                auth=(self.jira_email, self.jira_api_key)
            )
            
            if response.status_code == 200:
                user_info = response.json()
                print(f"✅ Jira connection successful! Connected as: {user_info.get('displayName')}")
                return True
            else:
                print(f"❌ Jira connection failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Jira connection error: {str(e)}")
            return False
    
    def test_tempo_connection(self):
        """Test Tempo API connection"""
        tempo_versions = [
            "https://api.tempo.io/core/3",
            "https://api.tempo.io/4", 
            "https://api.tempo.io/core/4",
            "https://api.tempo.io/v3",
            "https://api.tempo.io/v4"
        ]
        
        endpoints_to_try = ["/accounts", "/worklogs", "/teams"]
        
        for base_url in tempo_versions:
            print(f"Trying Tempo API version: {base_url}")
            for endpoint in endpoints_to_try:
                try:
                    url = f"{base_url}{endpoint}"
                    response = requests.get(url, headers=self.tempo_headers)
                    
                    if response.status_code == 200:
                        data = response.json()
                        print(f"✅ Tempo connection successful! URL: {url}")
                        self.tempo_base_url = base_url  # Update with working version
                        return True
                    elif response.status_code == 401:
                        print(f"❌ Authentication failed for {url}")
                        break  # Try next version
                    elif response.status_code == 403:
                        print(f"❌ Access forbidden for {url}")
                        break  # Try next version
                    else:
                        print(f"❌ {url} returned {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ Error testing {url}: {str(e)}")
                    continue
        
        print("❌ All Tempo API versions failed")
        return False
    
    def get_jira_issue(self, issue_key):
        """Get Jira issue details"""
        try:
            url = f"{self.jira_base_url}/rest/api/3/issue/{issue_key}"
            response = requests.get(
                url,
                headers=self.jira_headers,
                auth=(self.jira_email, self.jira_api_key)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get issue {issue_key}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting issue {issue_key}: {str(e)}")
            return None
    
    def get_tempo_worklogs(self, issue_key, from_date=None, to_date=None):
        """Get Tempo worklogs for a specific issue"""
        try:
            url = f"{self.tempo_base_url}/worklogs"
            
            params = {
                'jql': f'issue = {issue_key}'
            }
            
            if from_date:
                params['from'] = from_date.strftime('%Y-%m-%d')
            if to_date:
                params['to'] = to_date.strftime('%Y-%m-%d')
            
            response = requests.get(url, headers=self.tempo_headers, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get worklogs for {issue_key}: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting worklogs for {issue_key}: {str(e)}")
            return None
    
    def get_issue_hours(self, issue_key, from_date=None, to_date=None):
        """Get total hours tracked for a specific issue"""
        issue = self.get_jira_issue(issue_key)
        if not issue:
            return None
        
        worklogs = self.get_tempo_worklogs(issue_key, from_date, to_date)
        if not worklogs:
            return None
        
        total_seconds = 0
        worklog_details = []
        
        for worklog in worklogs.get('results', []):
            seconds = worklog.get('timeSpentSeconds', 0)
            total_seconds += seconds
            
            worklog_details.append({
                'author': worklog.get('author', {}).get('displayName', 'Unknown'),
                'date': worklog.get('startDate'),
                'hours': round(seconds / 3600, 2),
                'description': worklog.get('description', '')
            })
        
        total_hours = round(total_seconds / 3600, 2)
        
        return {
            'issue_key': issue_key,
            'issue_summary': issue.get('fields', {}).get('summary', ''),
            'total_hours': total_hours,
            'worklog_count': len(worklog_details),
            'worklogs': worklog_details
        }
    
    def get_user_hours_summary(self, user_identifier, from_date=None, to_date=None):
        """Get hours summary for a specific user"""
        try:
            # Try different ways to identify the user
            user_urls = [
                f"{self.tempo_base_url}/worklogs/user/{user_identifier}",
                f"{self.tempo_base_url}/worklogs?accountId={user_identifier}",
                f"{self.tempo_base_url}/worklogs?author={user_identifier}"
            ]
            
            params = {}
            if from_date:
                params['from'] = from_date.strftime('%Y-%m-%d')
            if to_date:
                params['to'] = to_date.strftime('%Y-%m-%d')
            
            for url in user_urls:
                response = requests.get(url, headers=self.tempo_headers, params=params)
                
                if response.status_code == 200:
                    worklogs = response.json()
                    total_seconds = sum(w.get('timeSpentSeconds', 0) for w in worklogs.get('results', []))
                    return {
                        'user': user_identifier,
                        'total_hours': round(total_seconds / 3600, 2),
                        'worklog_count': len(worklogs.get('results', [])),
                        'worklogs': worklogs.get('results', [])
                    }
                elif response.status_code == 404:
                    continue  # Try next URL format
                else:
                    print(f"❌ Failed to get user hours from {url}: {response.status_code} - {response.text}")
                    break
            
            return None
                
        except Exception as e:
            print(f"❌ Error getting user hours: {str(e)}")
            return None
    
    def load_config(self):
        """Load configuration including developer mappings"""
        try:
            with open('config.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            return None
    
    def get_all_jira_users(self):
        """Get all users from Jira"""
        try:
            url = f"{self.jira_base_url}/rest/api/3/users/search"
            params = {'maxResults': 1000}
            response = requests.get(
                url,
                headers=self.jira_headers,
                auth=(self.jira_email, self.jira_api_key),
                params=params
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get users: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error getting users: {str(e)}")
            return []
    
    def get_all_worklogs(self, from_date=None, to_date=None):
        """Get all worklogs for all developers"""
        try:
            url = f"{self.tempo_base_url}/worklogs"
            params = {}
            
            if from_date:
                params['from'] = from_date.strftime('%Y-%m-%d')
            if to_date:
                params['to'] = to_date.strftime('%Y-%m-%d')
            
            response = requests.get(url, headers=self.tempo_headers, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get all worklogs: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting all worklogs: {str(e)}")
            return None
    
    def get_user_info_by_account_id(self, account_id):
        """Get user info from Jira by account ID"""
        try:
            url = f"{self.jira_base_url}/rest/api/3/user"
            params = {'accountId': account_id}
            response = requests.get(
                url,
                headers=self.jira_headers,
                auth=(self.jira_email, self.jira_api_key),
                params=params
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            print(f"❌ Error getting user info for {account_id}: {str(e)}")
            return None
    
    def get_developer_hours_summary(self, from_date=None, to_date=None):
        """Get hours summary for all developers"""
        config = self.load_config()
        if not config:
            print("❌ Could not load config file")
            return None
        
        # Get all worklogs
        worklogs_data = self.get_all_worklogs(from_date, to_date)
        if not worklogs_data:
            return None
        
        worklogs = worklogs_data.get('results', [])
        
        # Get unique account IDs and resolve to user info
        account_ids = set()
        for worklog in worklogs:
            author_info = worklog.get('author', {})
            account_id = author_info.get('accountId')
            if account_id:
                account_ids.add(account_id)
        
        print(f"🔍 Resolving {len(account_ids)} user accounts...")
        
        # Cache user info
        user_cache = {}
        for account_id in account_ids:
            user_info = self.get_user_info_by_account_id(account_id)
            if user_info:
                user_cache[account_id] = {
                    'displayName': user_info.get('displayName', 'Unknown'),
                    'emailAddress': user_info.get('emailAddress', ''),
                    'accountId': account_id
                }
            else:
                user_cache[account_id] = {
                    'displayName': f'User-{account_id[-8:]}',
                    'emailAddress': '',
                    'accountId': account_id
                }
        
        # Group by author
        developer_hours = {}
        developer_mapping = config.get('developer_mapping', {}).get('global', {})
        
        for worklog in worklogs:
            author_info = worklog.get('author', {})
            account_id = author_info.get('accountId')
            
            if not account_id or account_id not in user_cache:
                continue
                
            user_info = user_cache[account_id]
            author_name = user_info['displayName']
            author_email = user_info['emailAddress']
            
            # Try to find mapped name
            mapped_name = author_name
            for git_name, display_name in developer_mapping.items():
                if (git_name.lower() in author_name.lower() or 
                    author_name.lower() in git_name.lower() or
                    (author_email and git_name.lower() in author_email.lower())):
                    mapped_name = display_name
                    break
            
            if mapped_name not in developer_hours:
                developer_hours[mapped_name] = {
                    'name': mapped_name,
                    'jira_name': author_name,
                    'email': author_email,
                    'account_id': account_id,
                    'total_seconds': 0,
                    'worklog_count': 0,
                    'worklogs': []
                }
            
            developer_hours[mapped_name]['total_seconds'] += worklog.get('timeSpentSeconds', 0)
            developer_hours[mapped_name]['worklog_count'] += 1
            
            # Get issue key if available
            issue_key = 'N/A'
            issue_info = worklog.get('issue', {})
            if issue_info and 'id' in issue_info:
                # Try to get issue key from Jira
                issue_data = self.get_jira_issue_by_id(issue_info['id'])
                if issue_data:
                    issue_key = issue_data.get('key', 'N/A')
            
            developer_hours[mapped_name]['worklogs'].append({
                'date': worklog.get('startDate'),
                'hours': round(worklog.get('timeSpentSeconds', 0) / 3600, 2),
                'issue': issue_key,
                'description': worklog.get('description', '')[:100]
            })
        
        # Convert to final format
        result = []
        for dev_name, data in developer_hours.items():
            result.append({
                'name': data['name'],
                'jira_name': data['jira_name'],
                'email': data['email'],
                'account_id': data['account_id'],
                'total_hours': round(data['total_seconds'] / 3600, 2),
                'worklog_count': data['worklog_count'],
                'worklogs': data['worklogs']
            })
        
        # Sort by total hours descending
        result.sort(key=lambda x: x['total_hours'], reverse=True)
        
        return result
    
    def get_jira_issue_by_id(self, issue_id):
        """Get Jira issue by ID"""
        try:
            url = f"{self.jira_base_url}/rest/api/3/issue/{issue_id}"
            response = requests.get(
                url,
                headers=self.jira_headers,
                auth=(self.jira_email, self.jira_api_key)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            return None

def main():
    """Test the Jira and Tempo connections"""
    try:
        tracker = JiraTempoTracker()
        
        print("🔍 Testing API connections...")
        print()
        
        # Test connections
        jira_ok = tracker.test_jira_connection()
        tempo_ok = tracker.test_tempo_connection()
        
        if not (jira_ok and tempo_ok):
            print("\n❌ One or more connections failed. Please check your configuration.")
            return
        
        print("\n✅ All connections successful!")
        print("\n" + "="*50)
        
        # Example usage
        print("📝 Example usage:")
        print("To get hours for a specific ticket, use:")
        print("  result = tracker.get_issue_hours('PROJ-123')")
        print("\nTo get hours for last 30 days:")
        print("  from_date = datetime.now() - timedelta(days=30)")
        print("  result = tracker.get_issue_hours('PROJ-123', from_date=from_date)")
        
        # Test with a sample issue (uncomment to test with real data)
        print("\n🔍 Would you like to test with a real Jira issue? (y/n)")
        user_input = input().lower().strip()
        
        if user_input == 'y':
            issue_key = input("Enter Jira issue key (e.g., CHIL-123): ").strip()
            if issue_key:
                print(f"\n🔍 Getting hours for issue: {issue_key}")
                result = tracker.get_issue_hours(issue_key)
                if result:
                    print(f"\n📊 Results for {result['issue_key']}:")
                    print(f"Summary: {result['issue_summary']}")
                    print(f"Total hours tracked: {result['total_hours']}")
                    print(f"Number of worklogs: {result['worklog_count']}")
                    
                    if result['worklogs']:
                        print(f"\n📝 Recent worklogs:")
                        for i, log in enumerate(result['worklogs'][:5]):  # Show first 5
                            print(f"  {i+1}. {log['author']} - {log['hours']}h on {log['date']}")
                            if log['description']:
                                print(f"     Description: {log['description'][:100]}...")
                else:
                    print("❌ Failed to get issue data")
        
        # Show user hours example
        print(f"\n🔍 Getting hours summary for {tracker.jira_email}...")
        user_hours = tracker.get_user_hours_summary(tracker.jira_email, from_date=datetime.now() - timedelta(days=7))
        if user_hours:
            print(f"📊 Your hours (last 7 days): {user_hours['total_hours']}h across {user_hours['worklog_count']} entries")
        
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("\nPlease make sure your .env file contains:")
        print("- JIRA_API_KEY")
        print("- JIRA_EMAIL") 
        print("- TEMPO_API_KEY")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()