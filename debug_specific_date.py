#!/usr/bin/env python3
"""Debug script to check specific date worklogs across all instances"""

import json
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from jira_integration import TempoIntegration

def debug_specific_date_worklogs():
    """Debug worklogs for 2025-08-15 across all instances"""
    print("🔍 Debugging Worklogs for 2025-08-15...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # Initialize TempoIntegration
    tempo = TempoIntegration(config)
    
    target_date = datetime.strptime('2025-08-15', '%Y-%m-%d').date()
    
    print(f"📅 Target Date: {target_date}")
    print(f"🏢 Total Instances: {len(tempo.tempo_keys)}")
    
    # Query each instance individually
    for instance_name, instance_config in tempo.tempo_keys.items():
        print(f"\n🔍 Querying Instance: {instance_name}")
        print("=" * 40)
        
        try:
            # Manually query this instance
            import requests
            
            url = f"{tempo.base_url}/worklogs"
            params = {
                'from': target_date.strftime('%Y-%m-%d'),
                'to': target_date.strftime('%Y-%m-%d'),
                'limit': 1000
            }
            
            response = requests.get(url, headers=instance_config['headers'], params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                worklogs = data.get('results', [])
                print(f"✅ Success: {len(worklogs)} worklogs found")
                
                # Show user breakdown
                users = {}
                for worklog in worklogs:
                    author = worklog.get('author', {})
                    account_id = author.get('accountId', 'Unknown')
                    
                    if account_id not in users:
                        users[account_id] = []
                    users[account_id].append(worklog)
                
                for user_id, user_worklogs in users.items():
                    total_hours = sum(w.get('timeSpentSeconds', 0) / 3600 for w in user_worklogs)
                    print(f"  👤 {user_id}: {total_hours:.1f}h ({len(user_worklogs)} logs)")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text[:200]}...")
        
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    # Now test the integrated approach
    print(f"\n🔄 Testing Integrated Approach...")
    print("=" * 40)
    
    worklogs = tempo.get_user_worklogs(target_date, target_date)
    print(f"Total developers with worklogs: {len(worklogs)}")
    
    for developer, logs in worklogs.items():
        total_hours = sum(log['time_spent_hours'] for log in logs)
        instances = set(log['tempo_instance'] for log in logs)
        print(f"👤 {developer}: {total_hours:.1f}h from instances: {', '.join(instances)}")

if __name__ == "__main__":
    debug_specific_date_worklogs()