#!/usr/bin/env python3
"""
AI Router Module
Provides a unified interface for different AI providers (OpenRouter, OpenAI, etc.)
"""

import requests
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
import logging

logger = logging.getLogger(__name__)


class AIProvider(ABC):
    """Abstract base class for AI providers"""
    
    @abstractmethod
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate a response from the AI model"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class OpenRouterProvider(AIProvider):
    """OpenRouter AI provider implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_key = config.get('api_key', '')
        self.base_url = config.get('base_url', 'https://openrouter.ai/api/v1')
        self.model = config.get('model', 'meta-llama/llama-3.1-8b-instruct:free')
        self.max_tokens = config.get('max_tokens', 1500)
        self.temperature = config.get('temperature', 0.7)
    
    def is_configured(self) -> bool:
        """Check if OpenRouter is properly configured"""
        return bool(self.api_key and self.base_url and self.model)
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using OpenRouter API"""
        if not self.is_configured():
            raise ValueError("OpenRouter provider is not properly configured")
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model,
                    "messages": messages,
                    "max_tokens": kwargs.get('max_tokens', self.max_tokens),
                    "temperature": kwargs.get('temperature', self.temperature)
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                error_msg = f"OpenRouter API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenRouter API request failed: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)


class OpenAIProvider(AIProvider):
    """OpenAI provider implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.api_key = config.get('api_key', '')
        self.model = config.get('model', 'gpt-4')
        self.max_tokens = config.get('max_tokens', 1500)
        self.temperature = config.get('temperature', 0.7)
    
    def is_configured(self) -> bool:
        """Check if OpenAI is properly configured"""
        return bool(self.api_key)
    
    def generate_response(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """Generate response using OpenAI API"""
        if not self.is_configured():
            raise ValueError("OpenAI provider is not properly configured")
        
        try:
            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model,
                    "messages": messages,
                    "max_tokens": kwargs.get('max_tokens', self.max_tokens),
                    "temperature": kwargs.get('temperature', self.temperature)
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                error_msg = f"OpenAI API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenAI API request failed: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)


class AIRouter:
    """Main AI router that manages different providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.providers = {}
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all available providers"""
        ai_config = self.config.get('ai', {})
        
        # Initialize OpenRouter provider
        if 'openrouter' in ai_config:
            self.providers['openrouter'] = OpenRouterProvider(ai_config['openrouter'])
        
        # Initialize OpenAI provider
        if 'openai' in ai_config:
            self.providers['openai'] = OpenAIProvider(ai_config['openai'])
    
    def get_provider(self, provider_name: Optional[str] = None) -> AIProvider:
        """Get a specific provider or the default one"""
        if provider_name is None:
            provider_name = self.config.get('ai', {}).get('provider', 'openrouter')
        
        if provider_name not in self.providers:
            raise ValueError(f"Provider '{provider_name}' is not available")
        
        provider = self.providers[provider_name]
        if not provider.is_configured():
            raise ValueError(f"Provider '{provider_name}' is not properly configured")
        
        return provider
    
    def generate_analysis(self, commits_data: List[Dict], date_str: str,
                         provider_name: Optional[str] = None,
                         jira_tempo_data: Dict = None,
                         developer_activity: Dict = None) -> Tuple[str, bool]:
        """Generate AI analysis of commit data

        Returns:
            Tuple[str, bool]: (analysis_text, success_flag)
        """
        if not commits_data:
            return f"No commits found for {date_str}.", True  # This is not an error

        # Prepare the analysis prompt
        analysis_prompt = self._create_analysis_prompt(commits_data, date_str, jira_tempo_data, developer_activity)

        messages = [
            {
                "role": "system",
                "content": "You are a senior software engineering manager analyzing developer performance based on git commit data. Provide constructive, detailed insights that are professional and actionable."
            },
            {
                "role": "user",
                "content": analysis_prompt
            }
        ]

        try:
            provider = self.get_provider(provider_name)
            analysis = provider.generate_response(messages)
            return analysis, True
        except Exception as e:
            error_msg = f"AI analysis failed: {str(e)}"
            logger.error(error_msg)
            return error_msg, False
    
    def _create_analysis_prompt(self, commits_data: List[Dict], date_str: str, jira_tempo_data: Dict = None, developer_activity: Dict = None) -> str:
        """Create the analysis prompt for AI"""

        # Group commits by developer
        developer_commits = {}
        for commit in commits_data:
            author = commit['author']
            if author not in developer_commits:
                developer_commits[author] = []
            developer_commits[author].append(commit)

        prompt = f"""
Analyze the following git commits from {date_str} and provide individual developer performance summaries:

DEVELOPER ACTIVITY:
"""

        for author, commits in developer_commits.items():
            total_files = sum(c['changes']['stats']['files_changed'] for c in commits)
            total_insertions = sum(c['changes']['stats']['insertions'] for c in commits)
            total_deletions = sum(c['changes']['stats']['deletions'] for c in commits)
            projects = list(set(c.get('project', 'Unknown') for c in commits))

            # Analyze actual code changes and complexity
            total_complexity_score = 0
            technical_areas = set()
            change_types = set()
            business_logic_changes = set()
            code_quality_indicators = set()
            meaningful_changes = 0
            formatting_changes = 0
            branches = set()

            # Aggregate detailed diff analysis
            for commit in commits:
                if 'changes' in commit:
                    diff_analysis = commit['changes'].get('diff_analysis', {})

                    # Aggregate complexity and change analysis
                    total_complexity_score += diff_analysis.get('complexity_score', 0)
                    technical_areas.update(diff_analysis.get('technical_areas', []))
                    change_types.update(diff_analysis.get('change_types', []))
                    business_logic_changes.update(diff_analysis.get('business_logic_changes', []))
                    code_quality_indicators.update(diff_analysis.get('code_quality_indicators', []))
                    meaningful_changes += diff_analysis.get('meaningful_changes', 0)
                    formatting_changes += diff_analysis.get('formatting_changes', 0)

                # Collect branch info
                if 'branch_info' in commit and commit['branch_info']:
                    branches.add(commit['branch_info'])

            # Calculate meaningful work ratio
            total_changes = meaningful_changes + formatting_changes
            meaningful_ratio = meaningful_changes / max(1, total_changes)

            # Get working time analysis from developer activity
            working_time = {}
            if developer_activity and author in developer_activity:
                working_time = developer_activity[author].get('working_time_analysis', {})

            time_range = working_time.get('time_range', 'Unknown')
            estimated_hours = working_time.get('estimated_hours', 0)
            work_pattern = working_time.get('work_pattern', 'Unknown')

            # Format analysis for AI
            technical_areas_str = ', '.join(list(technical_areas)[:3]) if technical_areas else 'general'
            change_types_str = ', '.join(list(change_types)[:4]) if change_types else 'basic_changes'
            business_logic_str = ', '.join(list(business_logic_changes)[:3]) if business_logic_changes else 'none'
            quality_indicators_str = ', '.join(list(code_quality_indicators)[:3]) if code_quality_indicators else 'none'
            branches_str = ', '.join(list(branches)[:3]) if branches else 'main'

            prompt += f"""
Developer: {author}
- Commits: {len(commits)}
- Projects: {', '.join(projects)}
- Branches: {branches_str}
- Files changed: {total_files}
- Lines: +{total_insertions}/-{total_deletions}
- Working time: {time_range} (~{estimated_hours}h, {work_pattern})
- Technical areas: {technical_areas_str}
- Change types: {change_types_str}
- Business logic: {business_logic_str}
- Code quality: {quality_indicators_str}
- Complexity score: {total_complexity_score}
- Meaningful changes: {meaningful_changes} ({meaningful_ratio:.1%} of total)
- Sample commits: {'; '.join([c['message'][:40] + '...' if len(c['message']) > 40 else c['message'] for c in commits[:2]])}
"""
            if len(commits) > 2:
                prompt += f"- ... and {len(commits) - 2} more commits\n"

            # Add specific code change examples
            sample_commit = commits[0] if commits else None
            if sample_commit and 'changes' in sample_commit:
                diff_analysis = sample_commit['changes'].get('diff_analysis', {})
                if diff_analysis.get('code_patterns'):
                    prompt += f"- Code patterns in latest commit: {', '.join(diff_analysis['code_patterns'][:3])}\n"
                if diff_analysis.get('business_logic_changes'):
                    prompt += f"- Business logic changes: {', '.join(diff_analysis['business_logic_changes'][:2])}\n"

            # Add Jira and Tempo information
            if jira_tempo_data and author in jira_tempo_data:
                jt_data = jira_tempo_data[author]

                if jt_data.get('commit_tickets'):
                    prompt += f"- Jira tickets worked on: {', '.join(jt_data['commit_tickets'][:3])}\n"

                tempo_info = jt_data.get('tempo_data', {})
                if tempo_info:
                    hours_logged = tempo_info.get('total_hours_logged', 0)
                    tickets_count = tempo_info.get('tickets_count', 0)
                    prompt += f"- Time logged: {hours_logged} hours on {tickets_count} tickets\n"

            prompt += "\n"

        prompt += """
Analyze the ACTUAL CODE CHANGES (not commit messages) and provide:

1. **BRIEF SUMMARY** (2-3 sentences): What technical work was accomplished based on diff analysis?

2. **INDIVIDUAL DEVELOPER PERFORMANCE** (for each developer):
   - Name: [Developer Name]
   - Code Analysis: [What they actually implemented based on complexity score, business logic changes, and technical areas]
   - Performance Rating: [X/5] (based on complexity score, meaningful changes ratio, and technical impact)
   - Technical Impact: [Specific assessment: new features/bug fixes/refactoring/infrastructure]
   - Code Quality: [Based on error handling, testing, security patterns found in diffs]

3. **TEAM METRICS**:
   - Total commits: [number]
   - Highest technical impact: [developer with highest complexity score]
   - Primary technical areas: [frontend/backend/database based on actual file changes]
   - Code quality focus: [testing/security/error handling based on diff patterns]

PERFORMANCE RATING CRITERIA (0-5):
- 5: High complexity score (>15), significant business logic changes, quality indicators (testing/security)
- 4: Good complexity score (8-15), meaningful feature development or substantial refactoring
- 3: Moderate complexity (4-8), standard feature work or bug fixes
- 2: Low complexity (1-4), minor changes or mostly formatting
- 1: Minimal complexity (<1), trivial changes or pure formatting
- 0: No meaningful code changes

Focus on:
- Complexity score and meaningful changes ratio
- Business logic vs formatting changes
- Technical areas and code quality indicators
- Actual implementation patterns found in diffs

IGNORE commit messages entirely - rate based on what code was actually modified/added/removed.
Keep response under 400 words.
"""

        return prompt
    
    def list_available_providers(self) -> List[str]:
        """List all available and configured providers"""
        available = []
        for name, provider in self.providers.items():
            if provider.is_configured():
                available.append(name)
        return available
