# Jira and Tempo Integration

The Developer Performance Analyzer now integrates with Jira and Tempo to provide comprehensive ticket tracking and time logging analysis.

## Features

### 🎫 Jira Integration
- **Automatic Ticket Extraction**: Extracts Jira ticket IDs from commit messages (e.g., CHIL-275, CHIL-239)
- **Ticket Details**: Retrieves ticket summary, status, priority, and assignee information
- **Developer-Ticket Mapping**: Shows which tickets each developer worked on

### ⏰ Tempo Integration  
- **Time Tracking**: Shows hours logged by each developer
- **Ticket-Time Correlation**: Links time logs to specific Jira tickets
- **Productivity Metrics**: Combines code commits with actual time spent

## Configuration

### 1. Environment Variables

Add to your `.env` file:

```bash
# Jira Integration
JIRA_API_KEY=ATATT3xFfGF0fDdI3pzMb78vZAXWRfLNwymgrYdTvMYBU7IB6thzm4aNF6QAWtuggAN_4YMQAgYvUkpvdDixk_VYVgcudUkpHIa44aCUYyZaiZdxbKdQGarjeua2CUxzP-RtdMjbwbM5ZmRoerLicPV7hgxFEPKoqurPsFs94FAlfzMPcBMR0mw=D67EF319
JIRA_EMAIL=<EMAIL>

# Tempo Time Tracking
TEMPO_API_KEY=A8e2D353E74xuVSv8asjRF4iehrHT5-us
```

### 2. Configuration File

The `config.json` includes:

```json
{
  "jira": {
    "enabled": true,
    "base_url": "https://childfreelegacy.atlassian.net",
    "api_key": "${JIRA_API_KEY}",
    "email": "${JIRA_EMAIL}",
    "project_key": "CHIL",
    "board_id": 2
  },
  "tempo": {
    "enabled": true,
    "api_key": "${TEMPO_API_KEY}",
    "base_url": "https://api.tempo.io/core/3"
  }
}
```

## How It Works

### 1. Ticket Extraction from Commits

The system automatically extracts Jira ticket IDs from commit messages:

```
Commit: "CHIL-275 Fixed UI notification display issues"
→ Extracts: CHIL-275

Commit: "CHIL-239 Updated JWT token logic with external ID"  
→ Extracts: CHIL-239
```

### 2. Enhanced Reports

#### Local Reports
```
Developer: Dmytro Haidamachenko
  Commits: 7
  Projects: Childfree Legacy
  Files changed: 92
  Lines: +365/-195
  Jira tickets: CHIL-283, CHIL-163, CHIL-289, CHIL-22
  Time logged: 6.5 hours on 4 tickets
```

#### Slack Reports
```
🧑‍💻 Dmytro Haidamachenko ⭐⭐⭐ (3/5)
• Complexity Score: 61
• Commits: 7 in Childfree Legacy
• Meaningful Changes: 194 (100% of total)
• Technical Areas: frontend
• Impact: Medium Impact, Feature Development
• Code Quality: security
• Jira Tickets: CHIL-283, CHIL-163, CHIL-289
• Time Logged: 6.5h on 4 tickets
```

## API Setup

### Jira API Key

1. Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
2. Click "Create API token"
3. Copy the token to your `.env` file as `JIRA_API_KEY`
4. Add your email address as `JIRA_EMAIL`

### Tempo API Key

1. Go to Tempo → Settings → API Integration
2. Create a new API token
3. Copy the token to your `.env` file as `TEMPO_API_KEY`

## Troubleshooting

### Common Issues

#### Jira 404 Errors
- **Cause**: Ticket doesn't exist or insufficient permissions
- **Solution**: Verify ticket IDs and API permissions

#### Tempo 410 Gone Error
- **Cause**: API endpoint changed or token expired
- **Solution**: Check Tempo API documentation for current endpoints

#### No Tickets Found
- **Cause**: Commit messages don't contain ticket IDs
- **Solution**: Ensure commits follow format: "CHIL-123 Description"

### Testing Integration

```bash
# Test with a specific date
python3 daily_analyzer.py --date 2025-08-15

# Check logs for integration status
tail -f logs/dev_analyzer.log
```

### Disabling Integration

To disable Jira or Tempo integration, set `enabled: false` in config.json:

```json
{
  "jira": {
    "enabled": false
  },
  "tempo": {
    "enabled": false
  }
}
```

## Benefits

### Enhanced Performance Assessment
- **Code + Time Correlation**: Compare actual coding work with time logged
- **Ticket Completion Tracking**: See which tickets developers actually worked on
- **Productivity Insights**: Identify discrepancies between commits and time logs

### Better Project Management
- **Ticket Progress**: Track which tickets had actual code changes
- **Developer Workload**: See both coding complexity and time investment
- **Sprint Analysis**: Understand what was actually accomplished vs planned

### Accurate Reporting
- **Stakeholder Updates**: Show both technical progress and time investment
- **Performance Reviews**: Data-driven assessment combining code quality and time management
- **Project Planning**: Historical data for better estimation

The integration provides a complete picture of developer productivity by combining code analysis, ticket tracking, and time logging into unified reports.
