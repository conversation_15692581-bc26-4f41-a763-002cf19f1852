#!/usr/bin/env python3
"""
Script to fetch Tempo logs from aleannlab.atlassian.net workspace
and help create user mappings for the configuration.
"""

import os
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Set
import sys

def load_config():
    """Load configuration from config.json"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ config.json not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing config.json: {e}")
        sys.exit(1)

def get_aleannlab_credentials():
    """Get the Tempo API key and Jira credentials for aleannlab workspace"""
    # Try to get from environment variable
    tempo_key = os.getenv('TEMPO_ALEANNLAB_API_KEY')
    jira_key = os.getenv('JIRA_ALEANNLAB_API_KEY')
    jira_email = os.getenv('JIRA_ALEANNLAB_EMAIL')

    if not tempo_key:
        print("❌ TEMPO_ALEANNLAB_API_KEY environment variable not found")
        print("Please set it in your .env file or environment")
        sys.exit(1)

    if not jira_key or not jira_email:
        print("⚠️ JIRA_ALEANNLAB_API_KEY or JIRA_ALEANNLAB_EMAIL not found")
        print("User names will not be resolved from Jira")
        return tempo_key, None, None

    return tempo_key, jira_key, jira_email

def fetch_tempo_worklogs(api_key: str, days_back: int = 30) -> List[Dict]:
    """Fetch Tempo worklogs for the specified period"""
    print(f"🔍 Fetching Tempo worklogs for last {days_back} days...")
    
    # Calculate date range
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days_back)
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    params = {
        'from': start_date.strftime('%Y-%m-%d'),
        'to': end_date.strftime('%Y-%m-%d'),
        'limit': 1000
    }
    
    try:
        url = "https://api.tempo.io/4/worklogs"
        print(f"📅 Date range: {start_date} to {end_date}")
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            worklogs = data.get('results', [])
            print(f"✅ Found {len(worklogs)} worklogs")
            return worklogs
        elif response.status_code == 401:
            print("❌ Authentication failed. Check your TEMPO_ALEANNLAB_API_KEY")
            return []
        elif response.status_code == 403:
            print("❌ Access forbidden. Check your Tempo API permissions")
            return []
        else:
            print(f"❌ API request failed: {response.status_code} - {response.text}")
            return []
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return []

def resolve_user_names_from_jira(account_ids: List[str], jira_key: str, jira_email: str) -> Dict[str, str]:
    """Resolve user names from Jira API"""
    if not jira_key or not jira_email:
        return {}

    print(f"🔍 Resolving {len(account_ids)} user names from Jira...")

    headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }

    resolved_names = {}

    for account_id in account_ids:
        try:
            url = f"https://aleannlab.atlassian.net/rest/api/3/user?accountId={account_id}"
            response = requests.get(url, headers=headers, auth=(jira_email, jira_key), timeout=10)

            if response.status_code == 200:
                user_data = response.json()
                display_name = user_data.get('displayName', 'Unknown')
                resolved_names[account_id] = display_name
                print(f"  ✅ {account_id} → {display_name}")
            else:
                print(f"  ❌ Failed to resolve {account_id}: {response.status_code}")
                resolved_names[account_id] = 'Unknown'

        except Exception as e:
            print(f"  ❌ Error resolving {account_id}: {e}")
            resolved_names[account_id] = 'Unknown'

    return resolved_names

def extract_unique_users(worklogs: List[Dict], resolved_names: Dict[str, str] = None) -> Dict[str, Dict]:
    """Extract unique users from worklogs"""
    users = {}

    for worklog in worklogs:
        author = worklog.get('author', {})
        account_id = author.get('accountId')
        display_name = author.get('displayName', 'Unknown')

        # Use resolved name if available
        if resolved_names and account_id in resolved_names:
            display_name = resolved_names[account_id]

        if account_id and account_id not in users:
            users[account_id] = {
                'account_id': account_id,
                'display_name': display_name,
                'worklog_count': 0,
                'total_hours': 0,
                'issues': set()
            }

        if account_id:
            users[account_id]['worklog_count'] += 1
            users[account_id]['total_hours'] += worklog.get('timeSpentSeconds', 0) / 3600

            # Track issues worked on
            issue = worklog.get('issue', {})
            issue_key = issue.get('key')
            if issue_key:
                users[account_id]['issues'].add(issue_key)

    # Convert sets to lists for JSON serialization
    for user_data in users.values():
        user_data['issues'] = list(user_data['issues'])
        user_data['total_hours'] = round(user_data['total_hours'], 2)

    return users

def match_users_to_known_names(users: Dict[str, Dict]) -> Dict[str, str]:
    """Match discovered users to known developer names from worklogs"""
    known_names = {
        'ivan kosovan': 'Ivan Kosovan',
        'ваня косован': 'Ivan Kosovan', 
        'rostyslav havryliuk': 'Rostyslav Havryliuk',
        'roman rasenko': 'Roman Rasenko',
        'ed sokolov': 'Ed Sokolov',
        'dmytro marianchenko': 'Dmytro Marianchenko',
        'dmitro zarutskyi': 'Dmitro Zarutskyi',
        'andrii korbutiak': 'Andrii Korbutiak',
        'andrii.korbutiak': 'Andrii Korbutiak'
    }
    
    mappings = {}
    
    for account_id, user_data in users.items():
        display_name = user_data['display_name'].lower()
        
        # Direct match
        if display_name in known_names:
            mappings[account_id] = known_names[display_name]
            continue
            
        # Partial match
        for known_key, known_value in known_names.items():
            if known_key in display_name or display_name in known_key:
                mappings[account_id] = known_value
                break
                
        # Name parts matching
        if account_id not in mappings:
            display_parts = display_name.split()
            for known_key, known_value in known_names.items():
                known_parts = known_key.split()
                if any(part in display_parts for part in known_parts if len(part) > 2):
                    mappings[account_id] = known_value
                    break
    
    return mappings

def generate_tempo_mapping_config(users: Dict[str, Dict], mappings: Dict[str, str]) -> Dict[str, str]:
    """Generate Tempo mapping configuration"""
    tempo_mapping = {}
    
    for account_id, user_data in users.items():
        if account_id in mappings:
            tempo_mapping[account_id] = mappings[account_id]
        else:
            # Use display name as fallback
            tempo_mapping[account_id] = user_data['display_name']
    
    return tempo_mapping

def update_config_file(tempo_mapping: Dict[str, str]):
    """Update config.json with new Tempo mappings"""
    config = load_config()
    
    # Update tempo mappings
    if 'developer_mapping' not in config:
        config['developer_mapping'] = {}
    if 'tempo' not in config['developer_mapping']:
        config['developer_mapping']['tempo'] = {}
    
    # Add new mappings (don't overwrite existing ones)
    existing_mappings = config['developer_mapping']['tempo']
    new_mappings_added = 0
    
    for account_id, name in tempo_mapping.items():
        if account_id not in existing_mappings:
            existing_mappings[account_id] = name
            new_mappings_added += 1
    
    # Write back to file
    try:
        with open('config.json', 'w') as f:
            json.dump(config, f, indent=2)
        print(f"✅ Added {new_mappings_added} new Tempo mappings to config.json")
    except Exception as e:
        print(f"❌ Error updating config.json: {e}")

def main():
    print("🚀 Aleannlab Tempo User Discovery Script")
    print("=" * 50)

    # Load configuration
    config = load_config()

    # Get credentials
    tempo_key, jira_key, jira_email = get_aleannlab_credentials()

    # Fetch worklogs
    worklogs = fetch_tempo_worklogs(tempo_key, days_back=60)  # Look back 60 days

    if not worklogs:
        print("❌ No worklogs found or API error")
        return

    # Get unique account IDs first
    account_ids = set()
    for worklog in worklogs:
        author = worklog.get('author', {})
        account_id = author.get('accountId')
        if account_id:
            account_ids.add(account_id)

    # Resolve user names from Jira
    resolved_names = {}
    if jira_key and jira_email:
        resolved_names = resolve_user_names_from_jira(list(account_ids), jira_key, jira_email)

    # Extract users with resolved names
    users = extract_unique_users(worklogs, resolved_names)
    print(f"\n👥 Found {len(users)} unique users:")

    # Display users with their activity
    for account_id, user_data in users.items():
        print(f"  • {user_data['display_name']}")
        print(f"    Account ID: {account_id}")
        print(f"    Worklogs: {user_data['worklog_count']}, Hours: {user_data['total_hours']}")
        print(f"    Issues: {', '.join(user_data['issues'][:5])}{'...' if len(user_data['issues']) > 5 else ''}")
        print()

    # Match to known names
    mappings = match_users_to_known_names(users)
    print(f"🔗 Matched {len(mappings)} users to known names:")
    for account_id, name in mappings.items():
        user_data = users[account_id]
        print(f"  • {user_data['display_name']} → {name}")
        print(f"    Account ID: {account_id}")

    # Generate Tempo mapping
    tempo_mapping = generate_tempo_mapping_config(users, mappings)

    print(f"\n📝 Generated Tempo Mapping Configuration:")
    print(json.dumps(tempo_mapping, indent=2))

    # Ask user if they want to update config
    response = input("\n❓ Update config.json with these mappings? (y/N): ").strip().lower()
    if response in ['y', 'yes']:
        update_config_file(tempo_mapping)
    else:
        print("ℹ️ Configuration not updated. You can manually add these mappings to config.json")

if __name__ == "__main__":
    main()
