#!/usr/bin/env python3
"""Test the report formatting specifically for <PERSON><PERSON><PERSON><PERSON>'s data"""

import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from daily_analyzer import DailyAnalyzer

def test_report_formatting():
    """Test the report formatting for <PERSON><PERSON><PERSON><PERSON> specifically"""
    print("🔍 Testing Report Formatting...")
    
    # Initialize analyzer
    analyzer = DailyAnalyzer('config.json')
    
    target_date = datetime.strptime('2025-08-15', '%Y-%m-%d').date()
    
    print(f"📅 Target Date: {target_date}")
    
    # Run the analysis
    analysis_result = analyzer.analyze_date(target_date)
    
    # Check jira_tempo_data
    jira_tempo_data = analysis_result.get('jira_tempo_data', {})
    print(f"📊 Jira Tempo Data: {len(jira_tempo_data)} developers")
    
    for developer, data in jira_tempo_data.items():
        print(f"\n👤 Developer: {developer}")
        tempo_data = data.get('tempo_data', {})
        hours = tempo_data.get('total_hours_logged', 0)
        has_logs = data.get('has_time_logs', False)
        print(f"  Hours: {hours}")
        print(f"  Has Time Logs: {has_logs}")
        
        if 'rostyslav' in developer.lower():
            print(f"  🎯 ROSTYSLAV FOUND!")
            print(f"  Full data: {data}")
    
    # Test the report formatting directly
    print(f"\n📝 Testing Report Formatting...")
    report_content = analyzer._format_text_report(analysis_result)
    
    # Find Rostyslav's section in the report
    lines = report_content.split('\n')
    rostyslav_section = []
    in_rostyslav_section = False
    
    for line in lines:
        if 'Rostyslav F' in line and 'Developer:' in line:
            in_rostyslav_section = True
            rostyslav_section.append(line)
        elif in_rostyslav_section:
            if line.startswith('Developer:') and 'Rostyslav' not in line:
                # Next developer section
                break
            rostyslav_section.append(line)
    
    print(f"🎯 Rostyslav's Report Section:")
    for line in rostyslav_section:
        print(f"  {line}")

if __name__ == "__main__":
    test_report_formatting()