#!/usr/bin/env python3
"""
Multi-Project Git Analysis Engine
Analyzes git commits across multiple repositories and aggregates developer data
"""

import subprocess
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class GitRepository:
    """Represents a single Git repository for analysis"""

    def __init__(self, project_config: Dict, global_mapping: Dict = None):
        self.name = project_config['name']
        self.enabled = project_config.get('enabled', True)

        # Handle new git structure
        git_config = project_config.get('git', {})
        if 'repository_url' in git_config:
            # Single repository
            self.repository_url = git_config['repository_url']
            self.local_path = git_config['local_path']
        elif 'repositories' in git_config:
            # Multiple repositories - use first one for now
            first_repo = git_config['repositories'][0]
            self.repository_url = first_repo['repository_url']
            self.local_path = first_repo['local_path']
        else:
            # Fallback to old structure for compatibility
            self.repository_url = project_config.get('repository_url', '')
            self.local_path = project_config.get('local_path', '')

        self.developer_mapping = project_config.get('developer_mapping', {})
        self.global_mapping = global_mapping or {}
    
    def ensure_repository(self) -> bool:
        """Ensure the repository exists locally and is up to date"""
        if not self.enabled:
            logger.info(f"Repository {self.name} is disabled, skipping")
            return False
        
        try:
            repo_path = Path(self.local_path)
            
            if not repo_path.exists():
                logger.info(f"Cloning repository {self.name} to {self.local_path}")
                repo_path.parent.mkdir(parents=True, exist_ok=True)
                
                result = subprocess.run([
                    'git', 'clone', self.repository_url, str(repo_path)
                ], capture_output=True, text=True, check=True)
                
                logger.info(f"Successfully cloned {self.name}")
            else:
                logger.info(f"Updating repository {self.name}")
                result = subprocess.run([
                    'git', '-C', str(repo_path), 'pull', 'origin', 'main'
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    # Try master branch if main fails
                    result = subprocess.run([
                        'git', '-C', str(repo_path), 'pull', 'origin', 'master'
                    ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"Successfully updated {self.name}")
                else:
                    logger.warning(f"Failed to update {self.name}: {result.stderr}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Error managing repository {self.name}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error with repository {self.name}: {e}")
            return False
    
    def normalize_author(self, author: str) -> str:
        """Normalize author name using global mapping first, then project-specific"""
        # First check global mapping
        if author in self.global_mapping:
            return self.global_mapping[author]
        # Then check project-specific mapping
        return self.developer_mapping.get(author, author)
    
    def get_commits_for_date(self, target_date: datetime.date) -> List[Dict]:
        """Get all commits for a specific date from this repository across all branches"""
        if not self.enabled:
            return []

        repo_path = Path(self.local_path)
        if not repo_path.exists():
            logger.warning(f"Repository path {self.local_path} does not exist")
            return []

        try:
            date_str = target_date.strftime('%Y-%m-%d')

            # Get commits from all branches for the target date
            result = subprocess.run([
                'git', '-C', str(repo_path), 'log',
                '--all',  # Include all branches
                '--since', f'{date_str} 00:00:00',
                '--until', f'{date_str} 23:59:59',
                '--pretty=format:%H|%an|%ae|%ad|%s|%D',  # Added %D for branch info
                '--date=iso'
            ], capture_output=True, text=True, check=True)

            commits = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.split('|', 5)
                    if len(parts) >= 5:
                        raw_author = parts[1]

                        # Skip bots and filtered users
                        if self.global_mapping and hasattr(self, '_should_skip_author'):
                            if self._should_skip_author(raw_author):
                                continue

                        author = self.normalize_author(raw_author)
                        branch_info = parts[5] if len(parts) > 5 else ""

                        # Parse timestamp for time analysis
                        from datetime import datetime
                        try:
                            # Handle different date formats
                            date_str = parts[3].strip()
                            if '+' in date_str:
                                # Format: 2025-08-15 01:07:14 +0300
                                date_part = date_str.split('+')[0].strip()
                                commit_datetime = datetime.strptime(date_part, '%Y-%m-%d %H:%M:%S')
                            elif 'Z' in date_str:
                                # Format: 2025-08-15T01:07:14Z
                                commit_datetime = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                            else:
                                # Try direct parsing
                                commit_datetime = datetime.fromisoformat(date_str)

                            commit_time = commit_datetime.strftime('%H:%M')
                            commit_hour = commit_datetime.hour
                        except Exception as e:
                            commit_time = 'Unknown'
                            commit_hour = 0

                        commits.append({
                            'hash': parts[0],
                            'author': author,
                            'raw_author': raw_author,
                            'email': parts[2],
                            'date': parts[3],
                            'commit_time': commit_time,
                            'commit_hour': commit_hour,
                            'commit_datetime': commit_datetime if 'commit_datetime' in locals() else None,
                            'message': parts[4],
                            'branch_info': branch_info,
                            'project': self.name,
                            'repository_path': str(repo_path)
                        })

            return commits

        except subprocess.CalledProcessError as e:
            logger.error(f"Error getting commits from {self.name}: {e}")
            return []
    
    def get_commit_changes(self, commit_hash: str) -> Dict:
        """Get detailed changes for a specific commit with enhanced diff analysis"""
        repo_path = Path(self.local_path)

        try:
            # Get file changes statistics
            result = subprocess.run([
                'git', '-C', str(repo_path), 'show', '--stat', '--format=', commit_hash
            ], capture_output=True, text=True, check=True)

            # Get actual diff with more context for better analysis
            # For large commits, limit the diff size to avoid overwhelming the analysis
            diff_result = subprocess.run([
                'git', '-C', str(repo_path), 'show', '--format=', '--unified=2',
                '--max-count=1', commit_hash
            ], capture_output=True, text=True, check=True)

            # If diff is too large, sample representative files
            diff_content = diff_result.stdout
            if len(diff_content) > 10000:  # If diff is very large
                # Get list of changed files and sample a few
                files_result = subprocess.run([
                    'git', '-C', str(repo_path), 'show', '--name-only', '--format=', commit_hash
                ], capture_output=True, text=True, check=True)

                changed_files = files_result.stdout.strip().split('\n')[:5]  # Sample first 5 files

                # Get diff for sampled files only
                sampled_diff = ""
                for file in changed_files:
                    if file.strip():
                        file_diff_result = subprocess.run([
                            'git', '-C', str(repo_path), 'show', '--format=', commit_hash, '--', file
                        ], capture_output=True, text=True, check=True)
                        sampled_diff += file_diff_result.stdout[:2000]  # Limit per file

                diff_content = sampled_diff

            # Get commit info (author, message, etc.)
            info_result = subprocess.run([
                'git', '-C', str(repo_path), 'show', '--format=%an|%ae|%ad|%s', '--no-patch', commit_hash
            ], capture_output=True, text=True, check=True)

            # Parse statistics
            stats = {'files_changed': 0, 'insertions': 0, 'deletions': 0, 'files': [], 'file_types': {}}
            lines = result.stdout.strip().split('\n')

            for line in lines:
                if '|' in line and ('+' in line or '-' in line):
                    parts = line.split('|')
                    if len(parts) >= 2:
                        filename = parts[0].strip()
                        changes = parts[1].strip()
                        stats['files'].append(filename)
                        stats['files_changed'] += 1

                        # Track file types
                        file_ext = filename.split('.')[-1] if '.' in filename else 'no_ext'
                        stats['file_types'][file_ext] = stats['file_types'].get(file_ext, 0) + 1

                        # Count insertions and deletions
                        plus_count = changes.count('+')
                        minus_count = changes.count('-')
                        stats['insertions'] += plus_count
                        stats['deletions'] += minus_count

            # Analyze diff content for better insights
            diff_analysis = self._analyze_diff_content(diff_content)

            return {
                'stats': stats,
                'diff': diff_content[:3000],  # Store more diff content for analysis
                'diff_analysis': diff_analysis,
                'commit_info': info_result.stdout.strip(),
                'is_sampled': len(diff_result.stdout) > 10000  # Flag if we sampled the diff
            }

        except subprocess.CalledProcessError as e:
            logger.error(f"Error getting commit changes for {commit_hash} in {self.name}: {e}")
            return {
                'stats': {'files_changed': 0, 'insertions': 0, 'deletions': 0, 'files': [], 'file_types': {}},
                'diff': '',
                'diff_analysis': {},
                'commit_info': ''
            }

    def _analyze_diff_content(self, diff_content: str) -> Dict:
        """Analyze diff content to extract meaningful insights about actual code changes"""
        analysis = {
            'code_patterns': [],
            'file_operations': [],
            'complexity_indicators': [],
            'change_types': [],
            'technical_areas': [],
            'code_quality_indicators': [],
            'business_logic_changes': [],
            'infrastructure_changes': [],
            'meaningful_changes': 0,
            'formatting_changes': 0,
            'complexity_score': 0
        }

        lines = diff_content.split('\n')
        added_lines = []
        removed_lines = []
        current_file = None

        for line in lines:
            original_line = line
            line = line.strip()

            # Track current file being analyzed
            if line.startswith('diff --git'):
                current_file = line.split()[-1] if len(line.split()) > 3 else None
                analysis['file_operations'].append('file_modified')
            elif line.startswith('new file mode'):
                analysis['file_operations'].append('file_created')
                analysis['complexity_score'] += 3  # New files are significant
            elif line.startswith('deleted file mode'):
                analysis['file_operations'].append('file_deleted')
                analysis['complexity_score'] += 2
            elif line.startswith('rename from'):
                analysis['file_operations'].append('file_renamed')
                analysis['complexity_score'] += 1

            # Analyze added lines (actual new code)
            if original_line.startswith('+') and not original_line.startswith('+++'):
                added_lines.append(line)
                self._analyze_code_line(line, analysis, 'added', current_file)

            # Analyze removed lines (deleted code)
            elif original_line.startswith('-') and not original_line.startswith('---'):
                removed_lines.append(line)
                self._analyze_code_line(line, analysis, 'removed', current_file)

        # Analyze patterns in added/removed code
        self._analyze_code_patterns(added_lines, removed_lines, analysis)

        # Determine overall change types and complexity
        self._determine_change_significance(analysis)

        return analysis

    def _analyze_code_line(self, line: str, analysis: Dict, change_type: str, current_file: str):
        """Analyze a single line of code change"""
        line_lower = line.lower()

        # Skip empty lines and simple formatting
        if not line.strip() or line.strip() in ['{', '}', '(', ')', ';', ',']:
            analysis['formatting_changes'] += 1
            return

        analysis['meaningful_changes'] += 1

        # Detect technical areas based on file type and content
        if current_file:
            if current_file.endswith(('.ts', '.tsx', '.js', '.jsx')):
                analysis['technical_areas'].append('frontend')
            elif current_file.endswith(('.py', '.java', '.cs', '.go')):
                analysis['technical_areas'].append('backend')
            elif current_file.endswith(('.sql', '.migration')):
                analysis['technical_areas'].append('database')
            elif current_file.endswith(('.css', '.scss', '.less')):
                analysis['technical_areas'].append('styling')
            elif current_file.endswith(('.json', '.yaml', '.yml', '.xml')):
                analysis['technical_areas'].append('configuration')

        # Analyze code complexity and patterns
        if change_type == 'added':
            # Function/method definitions
            if any(keyword in line_lower for keyword in ['function', 'def ', 'const ', 'let ', 'var ', 'class ']):
                analysis['code_patterns'].append('function_definition')
                analysis['complexity_score'] += 2

            # Business logic indicators
            if any(keyword in line_lower for keyword in ['if ', 'else', 'switch', 'case', 'for ', 'while ', 'foreach']):
                analysis['complexity_indicators'].append('conditional_logic')
                analysis['business_logic_changes'].append('control_flow')
                analysis['complexity_score'] += 1

            # Error handling
            if any(keyword in line_lower for keyword in ['try', 'catch', 'except', 'finally', 'throw', 'raise']):
                analysis['complexity_indicators'].append('error_handling')
                analysis['code_quality_indicators'].append('error_handling')
                analysis['complexity_score'] += 2

            # API/Database interactions
            if any(keyword in line_lower for keyword in ['fetch', 'axios', 'http', 'api', 'query', 'select', 'insert', 'update', 'delete']):
                analysis['business_logic_changes'].append('data_operations')
                analysis['complexity_score'] += 2

            # State management
            if any(keyword in line_lower for keyword in ['usestate', 'setstate', 'redux', 'context', 'provider']):
                analysis['business_logic_changes'].append('state_management')
                analysis['complexity_score'] += 2

            # Testing code
            if any(keyword in line_lower for keyword in ['test', 'expect', 'assert', 'mock', 'spy', 'describe', 'it(']):
                analysis['code_quality_indicators'].append('testing')
                analysis['complexity_score'] += 1

            # Security/validation
            if any(keyword in line_lower for keyword in ['validate', 'sanitize', 'auth', 'permission', 'security']):
                analysis['code_quality_indicators'].append('security')
                analysis['complexity_score'] += 2

            # Infrastructure/deployment
            if any(keyword in line_lower for keyword in ['docker', 'kubernetes', 'deploy', 'build', 'pipeline']):
                analysis['infrastructure_changes'].append('deployment')
                analysis['complexity_score'] += 1

    def _analyze_code_patterns(self, added_lines: list, removed_lines: list, analysis: Dict):
        """Analyze patterns across multiple lines to understand the nature of changes"""

        # Check for refactoring (similar amounts of additions and deletions)
        if len(added_lines) > 0 and len(removed_lines) > 0:
            ratio = min(len(added_lines), len(removed_lines)) / max(len(added_lines), len(removed_lines))
            if ratio > 0.7:  # Similar amounts of add/remove suggests refactoring
                analysis['change_types'].append('refactoring')

        # Check for bulk formatting changes
        formatting_indicators = 0
        for line in added_lines[:10]:  # Sample first 10 lines
            if any(indicator in line for indicator in ['  ', '\t', ';', '{', '}']) and len(line.strip()) < 5:
                formatting_indicators += 1

        if formatting_indicators > len(added_lines) * 0.5:
            analysis['change_types'].append('formatting')

        # Check for new feature development
        if len(added_lines) > len(removed_lines) * 2:
            analysis['change_types'].append('feature_development')

        # Check for bug fixes (small targeted changes)
        if len(added_lines) + len(removed_lines) < 20 and analysis['complexity_score'] > 0:
            analysis['change_types'].append('bug_fix')

    def _determine_change_significance(self, analysis: Dict):
        """Determine the overall significance and impact of changes"""

        # Calculate impact score
        meaningful_ratio = analysis['meaningful_changes'] / max(1, analysis['meaningful_changes'] + analysis['formatting_changes'])

        if meaningful_ratio > 0.8 and analysis['complexity_score'] > 5:
            analysis['change_types'].append('high_impact')
        elif meaningful_ratio > 0.6 and analysis['complexity_score'] > 2:
            analysis['change_types'].append('medium_impact')
        elif meaningful_ratio > 0.3:
            analysis['change_types'].append('low_impact')
        else:
            analysis['change_types'].append('minimal_impact')

        # Determine primary change type
        if 'feature_development' in analysis['change_types'] and analysis['complexity_score'] > 3:
            analysis['change_types'].append('new_feature')
        elif 'bug_fix' in analysis['change_types']:
            analysis['change_types'].append('maintenance')
        elif 'refactoring' in analysis['change_types']:
            analysis['change_types'].append('code_improvement')
        elif 'formatting' in analysis['change_types']:
            analysis['change_types'].append('code_cleanup')


class MultiProjectGitAnalyzer:
    """Analyzes git commits across multiple projects"""

    def __init__(self, config: Dict):
        self.config = config
        self.repositories = []
        self.global_developer_mapping = config.get('developer_mapping', {}).get('git', {})
        self.filtering_config = config.get('git', {})
        self._initialize_repositories()

    def _initialize_repositories(self):
        """Initialize all configured repositories"""
        projects = self.config.get('projects', [])
        for project_config in projects:
            if not project_config.get('enabled', True):
                continue

            git_config = project_config.get('git', {})
            if not git_config:
                continue

            # Handle multiple repositories per project
            if 'repositories' in git_config:
                # Multiple repositories (like TopProperty)
                for i, repo_config in enumerate(git_config['repositories']):
                    # Create a separate config for each repository
                    repo_project_config = {
                        'name': f"{project_config['name']} {i+1}" if len(git_config['repositories']) > 1 else project_config['name'],
                        'enabled': True,
                        'git': {
                            'repository_url': repo_config['repository_url'],
                            'local_path': repo_config['local_path']
                        }
                    }
                    repo = GitRepository(repo_project_config, self.global_developer_mapping)
                    self.repositories.append(repo)
                    logger.info(f"Initialized repository: {repo.name}")
            else:
                # Single repository
                repo = GitRepository(project_config, self.global_developer_mapping)
                self.repositories.append(repo)
                logger.info(f"Initialized repository: {repo.name}")

    def should_skip_author(self, author: str) -> bool:
        """Check if an author should be skipped (bots, etc.)"""
        # Check bot patterns
        skip_bots = self.filtering_config.get('skip_bots', [])
        for pattern in skip_bots:
            if pattern.replace('*', '') in author.lower():
                return True

        # Check explicit skip list
        skip_users = self.filtering_config.get('skip_users', [])
        if author in skip_users:
            return True

        return False

    def normalize_developer_name(self, author: str) -> str:
        """Normalize developer name using global mapping first, then project-specific"""
        # First check global mapping
        if author in self.global_developer_mapping:
            return self.global_developer_mapping[author]

        # If not found in global, return as-is
        return author
    
    def ensure_all_repositories(self) -> bool:
        """Ensure all repositories are available and up to date"""
        success = True
        for repo in self.repositories:
            if not repo.ensure_repository():
                success = False
        return success
    
    def get_commits_for_date(self, target_date: datetime.date) -> List[Dict]:
        """Get all commits from all repositories for a specific date"""
        all_commits = []
        
        for repo in self.repositories:
            commits = repo.get_commits_for_date(target_date)
            all_commits.extend(commits)
        
        # Sort commits by date
        all_commits.sort(key=lambda x: x['date'])
        
        return all_commits
    
    def get_commits_with_changes(self, target_date: datetime.date) -> List[Dict]:
        """Get commits with detailed change information"""
        commits = self.get_commits_for_date(target_date)
        
        commits_with_changes = []
        for commit in commits:
            # Find the repository for this commit
            repo = next((r for r in self.repositories if r.name == commit['project']), None)
            if repo:
                changes = repo.get_commit_changes(commit['hash'])
                commit['changes'] = changes
                commits_with_changes.append(commit)
        
        return commits_with_changes
    
    def aggregate_developer_activity(self, commits: List[Dict]) -> Dict[str, Dict]:
        """Aggregate activity by developer across all projects with bot filtering"""
        developer_activity = {}

        for commit in commits:
            # Skip bots and filtered users
            raw_author = commit.get('raw_author', commit['author'])
            if self.should_skip_author(raw_author):
                logger.debug(f"Skipping bot/filtered user: {raw_author}")
                continue

            # Normalize the author name using global mapping
            author = self.normalize_developer_name(commit['author'])

            if author not in developer_activity:
                developer_activity[author] = {
                    'total_commits': 0,
                    'projects': set(),
                    'branches': set(),
                    'total_files_changed': 0,
                    'total_insertions': 0,
                    'total_deletions': 0,
                    'file_types': {},
                    'change_types': [],
                    'commits': [],
                    'commit_times': [],
                    'commit_hours': [],
                    'working_time_analysis': {}
                }

            activity = developer_activity[author]
            activity['total_commits'] += 1
            activity['projects'].add(commit['project'])

            # Track branch information
            if 'branch_info' in commit and commit['branch_info']:
                activity['branches'].add(commit['branch_info'])

            # Track commit times
            if 'commit_time' in commit:
                activity['commit_times'].append(commit['commit_time'])
            if 'commit_hour' in commit:
                activity['commit_hours'].append(commit['commit_hour'])

            # Update the commit with normalized author name
            commit_copy = commit.copy()
            commit_copy['author'] = author
            activity['commits'].append(commit_copy)

            if 'changes' in commit:
                stats = commit['changes']['stats']
                activity['total_files_changed'] += stats['files_changed']
                activity['total_insertions'] += stats['insertions']
                activity['total_deletions'] += stats['deletions']

                # Aggregate file types
                for file_type, count in stats.get('file_types', {}).items():
                    activity['file_types'][file_type] = activity['file_types'].get(file_type, 0) + count

                # Aggregate change types
                diff_analysis = commit['changes'].get('diff_analysis', {})
                for change_type in diff_analysis.get('change_types', []):
                    if change_type not in activity['change_types']:
                        activity['change_types'].append(change_type)

        # Convert sets to lists for JSON serialization and analyze working times
        for author, activity in developer_activity.items():
            activity['projects'] = list(activity['projects'])
            activity['branches'] = list(activity['branches'])

            # Analyze working time patterns
            activity['working_time_analysis'] = self._analyze_working_time(activity)

        return developer_activity

    def _analyze_working_time(self, activity: Dict) -> Dict:
        """Analyze working time patterns from commit timestamps"""
        commit_hours = activity.get('commit_hours', [])
        commit_times = activity.get('commit_times', [])

        if not commit_hours:
            return {
                'time_range': 'Unknown',
                'estimated_hours': 0,
                'work_pattern': 'Unknown',
                'earliest_commit': 'Unknown',
                'latest_commit': 'Unknown'
            }

        # Calculate time range
        earliest_hour = min(commit_hours)
        latest_hour = max(commit_hours)

        # Calculate more accurate working hours based on commit clustering
        if len(commit_hours) == 1:
            estimated_hours = 1  # Single commit, assume 1 hour
        else:
            # Group commits into work sessions (commits within 2 hours of each other)
            sorted_hours = sorted(commit_hours)
            work_sessions = []
            current_session_start = sorted_hours[0]
            current_session_end = sorted_hours[0]

            for hour in sorted_hours[1:]:
                if hour - current_session_end <= 2:  # Within 2 hours = same session
                    current_session_end = hour
                else:  # Gap > 2 hours = new session
                    work_sessions.append((current_session_start, current_session_end))
                    current_session_start = hour
                    current_session_end = hour

            # Add the last session
            work_sessions.append((current_session_start, current_session_end))

            # Calculate total working hours from sessions
            estimated_hours = 0
            for start, end in work_sessions:
                session_duration = max(1, end - start + 1)  # At least 1 hour per session
                estimated_hours += min(session_duration, 4)  # Max 4 hours per session

            # Cap at reasonable daily maximum
            estimated_hours = min(estimated_hours, 10)

        # Determine work pattern
        morning_commits = sum(1 for h in commit_hours if 6 <= h < 12)
        afternoon_commits = sum(1 for h in commit_hours if 12 <= h < 18)
        evening_commits = sum(1 for h in commit_hours if 18 <= h < 24)
        night_commits = sum(1 for h in commit_hours if 0 <= h < 6)

        if morning_commits > afternoon_commits and morning_commits > evening_commits:
            work_pattern = 'Morning Worker'
        elif afternoon_commits > morning_commits and afternoon_commits > evening_commits:
            work_pattern = 'Afternoon Worker'
        elif evening_commits > morning_commits and evening_commits > afternoon_commits:
            work_pattern = 'Evening Worker'
        elif night_commits > 0:
            work_pattern = 'Night Worker'
        else:
            work_pattern = 'Mixed Schedule'

        # Format time range
        earliest_time = commit_times[commit_hours.index(earliest_hour)] if commit_times else f"{earliest_hour:02d}:00"
        latest_time = commit_times[commit_hours.index(latest_hour)] if commit_times else f"{latest_hour:02d}:00"

        if earliest_hour == latest_hour:
            time_range = f"~{earliest_time}"
        else:
            time_range = f"{earliest_time} - {latest_time}"

        return {
            'time_range': time_range,
            'estimated_hours': estimated_hours,
            'work_pattern': work_pattern,
            'earliest_commit': earliest_time,
            'latest_commit': latest_time,
            'work_sessions': len(work_sessions) if 'work_sessions' in locals() else 1,
            'commit_distribution': {
                'morning': morning_commits,
                'afternoon': afternoon_commits,
                'evening': evening_commits,
                'night': night_commits
            }
        }
    
    def generate_summary_stats(self, commits: List[Dict]) -> Dict:
        """Generate summary statistics for the analysis"""
        if not commits:
            return {
                'total_commits': 0,
                'total_developers': 0,
                'total_projects': 0,
                'total_files_changed': 0,
                'total_insertions': 0,
                'total_deletions': 0
            }
        
        projects = set(commit['project'] for commit in commits)
        developers = set(commit['author'] for commit in commits)
        
        total_files = sum(commit.get('changes', {}).get('stats', {}).get('files_changed', 0) for commit in commits)
        total_insertions = sum(commit.get('changes', {}).get('stats', {}).get('insertions', 0) for commit in commits)
        total_deletions = sum(commit.get('changes', {}).get('stats', {}).get('deletions', 0) for commit in commits)
        
        return {
            'total_commits': len(commits),
            'total_developers': len(developers),
            'total_projects': len(projects),
            'total_files_changed': total_files,
            'total_insertions': total_insertions,
            'total_deletions': total_deletions,
            'net_lines': total_insertions - total_deletions
        }
