# Tempo User Mapping Guide

## ✅ **Integration Status: WORKING!**

The Jira and Tempo integration is now fully functional with proper user mapping.

## 🎯 **Current Results**

### Working Time Comparison
```
Ivan D: 
  Commit-based time: ~6h (11:21 - 18:53, 2 sessions)
  Tempo logged time: 8.0h
  Discrepancy: 2h difference

D<PERSON><PERSON>:
  Commit-based time: ~5h (11:59 - 17:41, 2 sessions)  
  Tempo logged time: Not available

Maxim Sidorenko:
  Commit-based time: ~3h (01:07 - 10:54, 2 sessions)
  Tempo logged time: Not available
```

## 🔧 **Configuration Setup**

### 1. Environment Variables (.env)
```bash
JIRA_API_KEY=ATATT3xFfGF0fDdI3pzMb78vZAXWRfLNwymgrYdTvMYBU7IB6thzm4aNF6QAWtuggAN_4YMQAgYvUkpvdDixk_VYVgcudUkpHIa44aCUYyZaiZdxbKdQGarjeua2CUxzP-RtdMjbwbM5ZmRoer<PERSON>icPV7hgxFEPKoqurPsFs94FAlfzMPcBMR0mw=D67EF319
JIRA_EMAIL=<EMAIL>
TEMPO_API_KEY=A8e2D353E74xuVSv8asjRF4iehrHT5-us
```

### 2. Developer Mapping (config.json)
```json
{
  "developer_mapping": {
    "global": {
      "ivanDAleannlab": "Ivan D",
      "dhaidamachenko1": "Dmytro Haidamachenko",
      "maxims": "Maxim Sidorenko"
    },
    "tempo": {
      "User-cc7fe9a0": "Oleksii Stupak",
      "User-8cf57eb6": "Ivan D"
    }
  }
}
```

## 🔍 **How to Add New Tempo Users**

### Step 1: Identify Tempo User IDs
Run the daily analyzer and check the logs for "Unknown" users or use the debug script:

```bash
python3 daily_analyzer.py --date 2025-08-15
```

Look for Tempo users like `User-8cf57eb6` in the logs.

### Step 2: Map to Known Developers
Add the mapping to `config.json`:

```json
"tempo": {
  "User-8cf57eb6": "Ivan D",
  "User-cc7fe9a0": "Oleksii Stupak",
  "New-User-ID": "Developer Name"
}
```

### Step 3: Verify Mapping
Check the reports to ensure time is properly attributed:

```
Ivan D: Working time: 11:21 - 18:53 (~6h vs 8.0h logged, 2 sessions)
```

## 📊 **Enhanced Reports**

### Text Report
```
Developer: Ivan D
  Commits: 8
  Projects: Childfree Legacy
  Files changed: 25
  Lines: +347/-56
  Working time: 11:21 - 18:53 (~6h vs 8.0h logged, 2 sessions)
  Jira tickets: CHIL-274
  Time logged: 8.0 hours on 0 tickets
```

### Slack Report
```
🧑‍💻 Ivan D ⭐⭐⭐⭐⭐ (5/5)
• Complexity Score: 298
• Commits: 8 in Childfree Legacy
• Files changed: 25
• Lines: +347/-56
• Meaningful Changes: 453 (100% of total)
• Working Time: 11:21 - 18:53 (~6h vs 8.0h logged ⚠️, 2 sessions)
• Technical Areas: frontend
• Impact: New Feature, High Impact, Feature Development
• Code Quality: security, error_handling
• Jira Tickets: CHIL-274
```

## 🚨 **Discrepancy Detection**

The system automatically detects and flags discrepancies:

- **⚠️ Warning**: Shows when commit-based time differs significantly from logged time
- **Threshold**: 2+ hours difference triggers warning
- **Benefits**: Helps identify time tracking accuracy issues

## 🔧 **Troubleshooting**

### Common Issues

1. **"Unknown" Users in Reports**
   - **Cause**: New Tempo user not mapped
   - **Solution**: Add user to `tempo` mapping in config.json

2. **No Tempo Data**
   - **Cause**: API authentication or date range issues
   - **Solution**: Check TEMPO_API_KEY and date format

3. **Incorrect Time Attribution**
   - **Cause**: Wrong user mapping
   - **Solution**: Verify Tempo user ID and update mapping

### Debug Commands
```bash
# Test Tempo integration
python3 jira_tempo_tracker.py

# Check API keys
grep TEMPO_API_KEY .env

# Verify mapping
grep -A 10 "tempo" config.json
```

## ✅ **Benefits Achieved**

1. **Accurate Time Tracking**: Compare actual coding time vs logged time
2. **Productivity Insights**: Identify over/under-logging patterns  
3. **Project Management**: Better understanding of actual vs reported effort
4. **Performance Reviews**: Data-driven assessment with both code quality and time management
5. **Sprint Planning**: Historical data for better estimation

The integration now provides a complete picture of developer productivity! 🚀
