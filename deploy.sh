#!/bin/bash

# Git Analyzer Deployment Script
# This script updates the code on the server and ensures all dependencies are installed

set -e  # Exit on any error

echo "🚀 Starting Git Analyzer deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "daily_analyzer.py" ]; then
    print_error "Not in git-analyzer directory. Please run from /root/git-analyzer"
    exit 1
fi

print_status "Current directory: $(pwd)"

# Pull latest changes from GitHub
print_status "Pulling latest changes from GitHub..."
git fetch origin
git reset --hard origin/main

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_warning "Please update .env with your actual API keys!"
    else
        print_error ".env.example not found. Please create .env manually."
    fi
else
    print_status ".env file exists"
fi

# Install/update Python dependencies
print_status "Installing Python dependencies..."
pip3 install --upgrade pip
pip3 install requests python-dotenv

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p reports
mkdir -p logs
mkdir -p repositories

# Set proper permissions
print_status "Setting file permissions..."
chmod +x daily_analyzer.py
chmod +x deploy.sh
chmod 644 config.json
chmod 600 .env  # Secure the environment file

# Test the configuration
print_status "Testing configuration..."
if python3 -c "import json; json.load(open('config.json'))" 2>/dev/null; then
    print_status "✅ config.json is valid"
else
    print_error "❌ config.json is invalid"
    exit 1
fi

# Test environment variables
print_status "Checking environment variables..."
if python3 -c "from dotenv import load_dotenv; load_dotenv(); import os; print('✅ Environment loaded')" 2>/dev/null; then
    print_status "✅ Environment variables loaded successfully"
else
    print_warning "⚠️ Environment variables may have issues"
fi

# Check if cron job exists
print_status "Checking cron job..."
if crontab -l 2>/dev/null | grep -q "git-analyzer"; then
    print_status "✅ Cron job is installed"
else
    print_warning "⚠️ Cron job not found. Run: crontab -e"
fi

# Test multi-Jira setup if test script exists
if [ -f "test_multi_jira.py" ]; then
    print_status "Running multi-Jira configuration test..."
    if python3 test_multi_jira.py > /tmp/jira_test.log 2>&1; then
        print_status "✅ Multi-Jira test passed"
    else
        print_warning "⚠️ Multi-Jira test had issues. Check /tmp/jira_test.log"
    fi
fi

# Display status
print_status "Deployment completed successfully! 🎉"
print_status ""
print_status "📋 Next steps:"
print_status "1. Verify .env file has correct API keys"
print_status "2. Test manually: python3 daily_analyzer.py --date $(date -d yesterday +%Y-%m-%d)"
print_status "3. Check logs: tail -f logs/daily_analyzer.log"
print_status ""
print_status "⏰ Automated reports run daily at 8:50 AM Poland time"
print_status "🔄 Auto-deployment triggers on every push to main branch"

echo "✅ Deployment complete!"
