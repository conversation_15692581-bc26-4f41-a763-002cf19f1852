#!/usr/bin/env python3
"""
Discover <PERSON> Sid<PERSON>nko Tempo account ID(s) by scanning worklogs
- Uses Tempo API keys from config.json (all instances)
- Filters by names containing 'maxim', 'maksym', 'sidorenko'
- Prints candidate account IDs with hours and sample logs
"""

import os
import json
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv

load_dotenv()

def load_config():
    with open('config.json', 'r') as f:
        return json.load(f)

def fetch_worklogs(api_key: str, days_back: int = 30):
    base_url = 'https://api.tempo.io/4/worklogs'
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=days_back)
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    params = {
        'from': start_date.strftime('%Y-%m-%d'),
        'to': end_date.strftime('%Y-%m-%d'),
        'limit': 1000
    }
    r = requests.get(base_url, headers=headers, params=params, timeout=20)
    r.raise_for_status()
    return r.json().get('results', [])

KEYWORDS = ['maxim', 'maksym', 'sidorenko', 'max', 'm.sidor']

def main():
    cfg = load_config()
    instances = cfg.get('jira', {}).get('instances', [])
    print('Scanning Tempo instances for Maxim...')
    candidates = {}
    for inst in instances:
        name = inst.get('name')
        env_key = inst.get('tempo_api_key', '').strip('${}')
        api_key = os.getenv(env_key)
        if not api_key:
            print(f'- {name}: missing API key {env_key}')
            continue
        try:
            logs = fetch_worklogs(api_key, days_back=45)
            for wl in logs:
                author = wl.get('author', {})
                account_id = author.get('accountId')
                display_name = (author.get('displayName') or '').lower()
                desc = (wl.get('description') or '').lower()
                print(account_id, display_name, desc)
                if any(k in display_name or k in desc for k in KEYWORDS):
                    if account_id not in candidates:
                        candidates[account_id] = {
                            'instances': set(),
                            'display_names': set(),
                            'hours': 0.0,
                            'samples': []
                        }
                    c = candidates[account_id]
                    c['instances'].add(name)
                    if author.get('displayName'):
                        c['display_names'].add(author['displayName'])
                    c['hours'] += wl.get('timeSpentSeconds', 0) / 3600
                    if len(c['samples']) < 5:
                        c['samples'].append({
                            'date': wl.get('startDate'),
                            'desc': wl.get('description', '')[:80],
                            'issue': (wl.get('issue') or {}).get('key')
                        })
        except Exception as e:
            print(f'- {name}: error {e}')
    if not candidates:
        print('No candidates found.')
        return
    print('\nPotential Maxim accounts:')
    for acc_id, info in candidates.items():
        hours = info['hours']
        instances = ', '.join(sorted(info['instances']))
        print(f"* {acc_id} | hours={hours:.1f} | instances={instances}")
        if info['display_names']:
            names = ', '.join(sorted(info['display_names']))
            print(f"  Names: {names}")
        for s in info['samples']:
            date = s.get('date', '')
            issue = s.get('issue', '')
            desc = s.get('desc', '')
            print(f"  - {date} {issue}: {desc}")

if __name__ == '__main__':
    main()

