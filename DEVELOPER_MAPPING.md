# Developer Mapping Configuration

The Developer Performance Analyzer supports merging multiple Git usernames/aliases into a single developer identity. This is useful when developers use different usernames across projects or have variations in their Git configuration.

## Configuration Structure

Add a `developer_mapping` section to your `config.json`:

```json
{
  "developer_mapping": {
    "global": {
      "username1": "Real Developer Name",
      "username2": "Real Developer Name",
      "another_alias": "Another Developer"
    }
  }
}
```

## Example Configuration

```json
{
  "developer_mapping": {
    "global": {
      "ivanDAleannlab": "Ivan D",
      "IvanD": "Ivan D",
      "dhaidamachenko1": "<PERSON><PERSON><PERSON>",
      "maxims": "<PERSON> Sidorenko",
      "Oleksii": "Oleks<PERSON> Stupak",
      "comalex": "Oleks<PERSON> Stupak",
      "Oleksii Stupak": "Oleks<PERSON> Stupak"
    }
  }
}
```

## How It Works

1. **Global Mapping**: All Git usernames are first checked against the global mapping
2. **Normalization**: Multiple aliases are merged into a single developer identity
3. **Cross-Project**: Works across all projects in your configuration
4. **Aggregation**: Commits from all aliases are combined in reports

## Before and After Example

### Before (without mapping):
```
DEVELOPER ACTIVITY:
- ivanDAleannlab: 5 commits
- IvanD: 2 commits
- dhaidamachenko1: 8 commits
- maxims: 6 commits
```

### After (with mapping):
```
DEVELOPER ACTIVITY:
- Ivan D: 7 commits (merged from ivanDAleannlab + IvanD)
- Dmytro Haidamachenko: 8 commits
- Maxim Sidorenko: 6 commits
```

## AI Analysis Impact

The AI analysis will also reflect the merged identities:

```
**INDIVIDUAL DEVELOPER PERFORMANCE**:

- **Ivan D**:
  - Summary: Ivan contributed by adding logic for fetching invites and improving user experience.
  - Performance Rating: 3.8/5
  - Key Focus: Invite management and user interactions.
```

## Best Practices

1. **Use Full Names**: Map to full, professional names for reports
2. **Consistent Naming**: Use the same target name across all aliases
3. **Case Sensitivity**: Mappings are case-sensitive
4. **Regular Updates**: Add new aliases as developers join or change usernames

## Legacy Project-Specific Mapping

You can still use project-specific mappings if needed:

```json
{
  "projects": [
    {
      "name": "Legacy Project",
      "developer_mapping": {
        "old_username": "New Developer Name"
      }
    }
  ]
}
```

**Note**: Global mappings take precedence over project-specific mappings.

## Testing Your Configuration

After updating your developer mapping, test it:

```bash
# Run analysis for a recent date
python3 daily_analyzer.py --date 2025-08-12

# Check the report to verify merging worked
cat reports/daily_report_2025-08-12.txt
```

Look for:
- Reduced number of unique developers
- Combined commit counts
- Merged activity in AI analysis
