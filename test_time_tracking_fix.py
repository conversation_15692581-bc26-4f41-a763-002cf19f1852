#!/usr/bin/env python3
"""
Test script to verify that ALL developers get Time Tracked field in reports
"""

import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from jira_integration import <PERSON>raT<PERSON><PERSON>A<PERSON>yzer
from slack_notifier import SlackNotifier

def test_all_developers_get_time_tracking():
    """Test that all developers get time tracking data, even if 0 hours"""
    print("🧪 Testing Time Tracking for All Developers...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    analyzer = JiraTempoAnalyzer(config)
    slack_notifier = SlackNotifier(config)
    
    # Create test commits from all known developers
    test_commits = [
        {'author': '<PERSON><PERSON><PERSON>', 'message': 'CHIL-123: Test commit', 'date': '2025-08-15'},
        {'author': 'Ivan D', 'message': 'XDPA-456: Test commit', 'date': '2025-08-15'},
        {'author': '<PERSON><PERSON><PERSON>', 'message': 'CHIL-789: Test commit', 'date': '2025-08-15'},
        {'author': '<PERSON><PERSON><PERSON><PERSON>', 'message': 'XDPA-101: Test commit', 'date': '2025-08-15'},
        {'author': 'Dmytro Marjan', 'message': 'SCRUM-202: Test commit', 'date': '2025-08-15'},
        {'author': 'Maxim Sidorenko', 'message': 'CHIL-303: Test commit', 'date': '2025-08-15'},
        {'author': 'The Stink Master', 'message': 'CHIL-404: Test commit', 'date': '2025-08-15'},
        {'author': 'Dmytro Perepelytsa', 'message': 'CHIL-505: Test commit', 'date': '2025-08-15'},
        # Test with git usernames too
        {'author': 'comalex', 'message': 'CHIL-606: Test commit', 'date': '2025-08-15'},
        {'author': 'ivanDAleannlab', 'message': 'XDPA-707: Test commit', 'date': '2025-08-15'},
        {'author': 'rostyslaf', 'message': 'XDPA-808: Test commit', 'date': '2025-08-15'},
    ]
    
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=1)
    
    print(f"📅 Testing with {len(test_commits)} developers for date range {start_date} to {end_date}")
    
    try:
        # Run the analysis
        results = analyzer.analyze_developer_tickets_and_time(test_commits, start_date, end_date)
        
        print("\n📊 Analysis Results:")
        print("=" * 70)
        
        all_have_time_field = True
        developers_with_time = 0
        developers_without_time = 0
        
        for developer, data in results.items():
            has_commits = data.get('has_commits', False)
            has_time_logs = data.get('has_time_logs', False)
            tempo_data = data.get('tempo_data', {})
            total_hours = tempo_data.get('total_hours', 0)
            
            commit_status = "✅" if has_commits else "❌"
            time_status = "✅" if has_time_logs else "⚠️"
            
            print(f"🧑‍💻 {developer}")
            print(f"   Commits: {commit_status} ({len(data.get('commit_tickets', []))} tickets)")
            print(f"   Time Logs: {time_status} ({total_hours:.1f}h)")
            print(f"   Tempo Data Structure: {bool(tempo_data)}")
            
            if has_time_logs:
                developers_with_time += 1
            else:
                developers_without_time += 1
            
            print()
        
        print(f"📈 Summary:")
        print(f"   Total developers: {len(results)}")
        print(f"   With time logs: {developers_with_time}")
        print(f"   Without time logs: {developers_without_time}")
        print(f"   All have tempo_data structure: {all([bool(d.get('tempo_data')) for d in results.values()])}")
        
        # Test Slack formatting
        print("\n📱 Testing Slack Message Formatting...")
        print("=" * 50)
        
        # Create fake developer activity data
        developer_activity = {}
        for commit in test_commits:
            author = commit['author']
            if author not in developer_activity:
                developer_activity[author] = {
                    'commits': [commit],
                    'total_commits': 1,
                    'lines_added': 10,
                    'lines_removed': 5,
                    'files_changed': 2,
                    'first_commit': commit['date'],
                    'last_commit': commit['date'],
                    'commit_times': ['10:00'],
                    'working_hours': {'start': '09:00', 'end': '17:00'},
                    'estimated_hours': 6.5
                }
        
        # Test the Slack formatting
        blocks = slack_notifier.format_developer_summary(
            developer_activity, 
            '2025-08-15', 
            results,  # This is the jira_tempo_data
            "Test AI analysis"
        )
        
        # Check if all developers have "Time Tracked" in their blocks
        developers_in_slack = 0
        developers_with_time_tracked = 0
        
        for block in blocks:
            if block.get('type') == 'section' and 'text' in block:
                text = block['text'].get('text', '')
                if '🧑‍💻' in text:  # This is a developer section
                    developers_in_slack += 1
                    if 'Time Tracked:' in text:
                        developers_with_time_tracked += 1
                        print(f"✅ Developer section has 'Time Tracked' field")
                    else:
                        print(f"❌ Developer section missing 'Time Tracked' field")
                        print(f"   Text: {text[:200]}...")
        
        print(f"\n📊 Slack Formatting Results:")
        print(f"   Developer sections in Slack: {developers_in_slack}")
        print(f"   Sections with 'Time Tracked': {developers_with_time_tracked}")
        
        if developers_with_time_tracked == developers_in_slack:
            print("✅ SUCCESS: All developers have 'Time Tracked' field!")
        else:
            print("❌ ISSUE: Some developers missing 'Time Tracked' field!")
            
        return developers_with_time_tracked == developers_in_slack
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("🧪 Time Tracking Fix Verification")
    print("=" * 60)
    
    success = test_all_developers_get_time_tracking()
    
    if success:
        print("\n🎉 SUCCESS: All developers will have 'Time Tracked' field!")
        print("\n📋 Next Steps:")
        print("1. Deploy changes to server")
        print("2. Run: python3 daily_analyzer.py --date 2025-08-15")
        print("3. Check that ALL developers show 'Time Tracked' field")
        print("4. Verify Slack report shows time tracking for everyone")
    else:
        print("\n❌ ISSUE: Some developers still missing 'Time Tracked' field")
        print("\n🔧 Troubleshooting:")
        print("1. Check Tempo mapping in config.json")
        print("2. Verify developer name normalization")
        print("3. Test with debug_tempo_users.py")

if __name__ == "__main__":
    main()
