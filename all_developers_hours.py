#!/usr/bin/env python3
"""
Get working hours for all developers using Jira and Tempo APIs
"""

from jira_tempo_tracker import JiraTempoTracker
from datetime import datetime, timedelta
import sys

def main():
    try:
        tracker = JiraTempoTracker()
        
        print("🔍 Testing API connections...")
        
        # Test connections
        jira_ok = tracker.test_jira_connection()
        tempo_ok = tracker.test_tempo_connection()
        
        if not (jira_ok and tempo_ok):
            print("\n❌ One or more connections failed. Please check your configuration.")
            return
        
        print("\n✅ All connections successful!")
        print("\n" + "="*80)
        
        # Get date range - default last 7 days, or custom
        if len(sys.argv) > 1:
            days_back = int(sys.argv[1])
        else:
            days_back = 7
            
        from_date = datetime.now() - timedelta(days=days_back)
        to_date = datetime.now()
        
        print(f"📊 Getting hours for ALL developers (last {days_back} days)")
        print(f"📅 From: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")
        print("\n⏳ Fetching data from Tempo API...")
        
        # Get all developer hours
        all_dev_hours = tracker.get_developer_hours_summary(from_date=from_date, to_date=to_date)
        
        if not all_dev_hours:
            print("❌ Failed to get developer hours data")
            return
        
        print(f"\n✅ Found {len(all_dev_hours)} developers with logged hours")
        print("\n" + "="*80)
        
        total_hours_all = sum(dev['total_hours'] for dev in all_dev_hours)
        total_entries_all = sum(dev['worklog_count'] for dev in all_dev_hours)
        
        print(f"📈 SUMMARY - Last {days_back} days:")
        print(f"   Total Hours: {total_hours_all}h")
        print(f"   Total Entries: {total_entries_all}")
        print(f"   Active Developers: {len(all_dev_hours)}")
        
        print(f"\n👥 DEVELOPER BREAKDOWN:")
        print("-" * 80)
        
        for i, dev in enumerate(all_dev_hours, 1):
            percentage = (dev['total_hours'] / total_hours_all * 100) if total_hours_all > 0 else 0
            print(f"{i:2d}. {dev['name']:<25} {dev['total_hours']:>6.1f}h ({percentage:4.1f}%) - {dev['worklog_count']:>3} entries")
            
            if dev['jira_name'] != dev['name']:
                print(f"     Jira Name: {dev['jira_name']}")
            if dev['email']:
                print(f"     Email: {dev['email']}")
        
        print("\n" + "="*80)
        print("🔍 DETAILED WORKLOG BREAKDOWN:")
        
        for i, dev in enumerate(all_dev_hours, 1):
            if dev['total_hours'] > 0:
                print(f"\n{i}. {dev['name']} - {dev['total_hours']}h:")
                
                # Group by issue
                issues = {}
                for log in dev['worklogs']:
                    issue = log['issue']
                    if issue not in issues:
                        issues[issue] = {'hours': 0, 'entries': 0, 'dates': set()}
                    issues[issue]['hours'] += log['hours']
                    issues[issue]['entries'] += 1
                    issues[issue]['dates'].add(log['date'])
                
                # Show top issues
                sorted_issues = sorted(issues.items(), key=lambda x: x[1]['hours'], reverse=True)
                for issue, data in sorted_issues[:5]:  # Top 5 issues
                    date_range = f"{min(data['dates'])} to {max(data['dates'])}" if len(data['dates']) > 1 else list(data['dates'])[0]
                    print(f"   • {issue:<12} {data['hours']:>5.1f}h ({data['entries']:>2} entries) - {date_range}")
                
                if len(sorted_issues) > 5:
                    remaining = len(sorted_issues) - 5
                    remaining_hours = sum(data['hours'] for _, data in sorted_issues[5:])
                    print(f"   • ... and {remaining} more issues ({remaining_hours:.1f}h)")
        
        print(f"\n💡 Usage: python {sys.argv[0]} [days_back]")
        print(f"   Example: python {sys.argv[0]} 14  # Get last 14 days")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()