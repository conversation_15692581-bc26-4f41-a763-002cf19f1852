#!/usr/bin/env python3
"""
Script to fix and test Slack hours reporting functionality.
Ensures that time tracking hours are properly displayed in daily reports.
"""

import os
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add the current directory to Python path
script_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(script_dir))

try:
    from daily_analyzer import DailyAnalyzer
    from jira_integration import JiraTempoAnalyzer
except ImportError as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)

def load_config():
    """Load configuration from config.json"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ config.json not found")
        sys.exit(1)

def test_tempo_integration():
    """Test Tempo integration for all projects"""
    print("🔍 Testing Tempo Integration")
    print("=" * 40)
    
    config = load_config()
    projects = config.get('projects', [])
    
    # Test each project's Tempo integration
    for project in projects:
        if not project.get('enabled', True):
            continue
            
        project_name = project['name']
        tempo_config = project.get('tempo')
        
        if not tempo_config:
            print(f"⚠️ {project_name}: No Tempo configuration")
            continue
            
        print(f"\n📋 Testing {project_name}...")
        
        # Get API key
        tempo_key_env = tempo_config['api_key'].strip('${}')
        tempo_key = os.getenv(tempo_key_env)
        
        if not tempo_key:
            print(f"  ❌ Missing API key: {tempo_key_env}")
            continue
            
        # Test API connection
        try:
            import requests
            headers = {
                'Authorization': f'Bearer {tempo_key}',
                'Accept': 'application/json'
            }
            
            # Test with recent date range
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)
            
            params = {
                'from': start_date.strftime('%Y-%m-%d'),
                'to': end_date.strftime('%Y-%m-%d'),
                'limit': 10
            }
            
            response = requests.get('https://api.tempo.io/4/worklogs', 
                                  headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                worklogs = data.get('results', [])
                
                # Calculate total hours
                total_hours = sum(w.get('timeSpentSeconds', 0) for w in worklogs) / 3600
                
                print(f"  ✅ API working: {len(worklogs)} worklogs, {total_hours:.1f}h in last 7 days")
                
                # Show sample worklog
                if worklogs:
                    sample = worklogs[0]
                    author = sample.get('author', {})
                    print(f"  📝 Sample: {author.get('displayName', 'Unknown')} - {sample.get('timeSpentSeconds', 0)/3600:.1f}h")
                    
            else:
                print(f"  ❌ API error: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Connection error: {e}")

def test_jira_tempo_analyzer():
    """Test the JiraTempoAnalyzer functionality"""
    print("\n🔍 Testing JiraTempoAnalyzer")
    print("=" * 40)
    
    try:
        config = load_config()
        analyzer = JiraTempoAnalyzer(config)
        
        # Test with yesterday's date
        yesterday = datetime.now().date() - timedelta(days=1)
        
        print(f"📅 Testing with date: {yesterday}")
        
        # Get some sample commits (empty for testing)
        sample_commits = []
        
        # Test the analyzer
        result = analyzer.analyze_developer_tickets_and_time(
            sample_commits, yesterday, yesterday
        )
        
        print(f"✅ Analyzer returned data for {len(result)} developers")
        
        # Show sample data
        for dev_name, dev_data in list(result.items())[:3]:
            tempo_data = dev_data.get('tempo_data', {})
            total_hours = tempo_data.get('total_hours_logged', 0)
            print(f"  • {dev_name}: {total_hours}h logged")
            
    except Exception as e:
        print(f"❌ JiraTempoAnalyzer test failed: {e}")

def test_slack_hours_summary():
    """Test the Slack hours summary functionality"""
    print("\n🔍 Testing Slack Hours Summary")
    print("=" * 40)
    
    try:
        analyzer = DailyAnalyzer("config.json")
        
        # Test with yesterday's date
        yesterday = datetime.now().date() - timedelta(days=1)
        
        print(f"📅 Testing hours summary for: {yesterday}")
        
        # Run analysis to get data
        analysis_result = analyzer.analyze_date(yesterday)
        
        if analysis_result:
            jira_tempo_data = analysis_result.get('jira_tempo_data', {})
            
            if jira_tempo_data:
                print(f"✅ Found Jira/Tempo data for {len(jira_tempo_data)} developers")
                
                # Extract hours data like the real function does
                hours_data = []
                total_hours = 0
                
                for dev_name, dev_data in jira_tempo_data.items():
                    tempo_info = dev_data.get('tempo_data', {})
                    hours_logged = tempo_info.get('total_hours_logged', 0)
                    
                    if hours_logged > 0:
                        tickets = len(dev_data.get('commit_tickets', []))
                        hours_data.append({
                            'name': dev_name,
                            'hours': hours_logged,
                            'tickets': tickets
                        })
                        total_hours += hours_logged
                
                if hours_data:
                    print(f"✅ Hours summary data:")
                    print(f"   Total developers with logged time: {len(hours_data)}")
                    print(f"   Total hours logged: {total_hours:.1f}h")
                    
                    for data in hours_data[:5]:  # Show first 5
                        print(f"   • {data['name']}: {data['hours']:.1f}h ({data['tickets']} tickets)")
                        
                    # Test sending the summary (dry run)
                    print(f"\n📤 Testing Slack message format...")
                    
                    message = f"⏰ **Time Tracking Summary - {yesterday}**\n\n"
                    message += "```\n"
                    message += "Developer               Hours    Tickets\n"
                    message += "─" * 40 + "\n"

                    for data in hours_data:
                        name = data['name'][:20].ljust(20)
                        hours = f"{data['hours']:.1f}h".rjust(7)
                        tickets = f"{data['tickets']}".rjust(7)
                        message += f"{name} {hours} {tickets}\n"

                    message += "─" * 40 + "\n"
                    total_hours_str = f"{total_hours:.1f}h".rjust(7)
                    message += f"{'TOTAL'.ljust(20)} {total_hours_str}\n"
                    message += "```\n"
                    
                    print("✅ Slack message format looks good!")
                    print("Preview:")
                    print(message[:200] + "..." if len(message) > 200 else message)
                    
                else:
                    print("⚠️ No developers with logged hours found")
            else:
                print("⚠️ No Jira/Tempo data found")
        else:
            print("❌ Analysis failed")
            
    except Exception as e:
        print(f"❌ Slack hours summary test failed: {e}")

def run_test_analysis():
    """Run a complete test analysis"""
    print("\n🚀 Running Complete Test Analysis")
    print("=" * 50)
    
    try:
        analyzer = DailyAnalyzer("config.json")
        
        # Test with yesterday
        yesterday = datetime.now().date() - timedelta(days=1)
        print(f"📅 Testing complete analysis for: {yesterday}")
        
        # Run the analysis
        success = analyzer.run_daily_analysis(target_date=yesterday)
        
        if success:
            print("✅ Complete analysis test successful!")
            print("   Check your Slack channel for the test report.")
        else:
            print("❌ Complete analysis test failed")
            
    except Exception as e:
        print(f"❌ Complete test failed: {e}")

def main():
    print("🔧 Slack Hours Reporting Fix & Test")
    print("=" * 50)
    
    # Check environment
    required_vars = [
        'SLACK_TOKEN',
        'TEMPO_ALEANNLAB_API_KEY',
        'JIRA_ALEANNLAB_API_KEY',
        'JIRA_ALEANNLAB_EMAIL'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set them in your .env file")
        return
    
    print("✅ Environment variables check passed")
    
    # Run tests
    test_tempo_integration()
    test_jira_tempo_analyzer()
    test_slack_hours_summary()
    
    # Ask if user wants to run complete test
    print("\n" + "=" * 50)
    response = input("🤔 Run complete test analysis and send to Slack? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        run_test_analysis()
    else:
        print("ℹ️ Skipping complete test. Use --test flag on daily analyzer to test.")

if __name__ == "__main__":
    main()
