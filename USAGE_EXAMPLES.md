# Usage Examples

This document provides practical examples for the Developer Performance Analyzer.

## 🚀 Most Common Usage

### 1. Daily Analysis (Yesterday)
```bash
# Analyze yesterday's commits - most common usage
python3 daily_analyzer.py
```
**What it does:**
- Analyzes commits from yesterday across all enabled projects
- Merges developer aliases (e.g., `dim<PERSON>rjanAL` + `<PERSON><PERSON><PERSON><PERSON> = `<PERSON><PERSON><PERSON>)
- Filters out bots (e.g., `gpt-engineer-app[bot]`)
- Analyzes all branches, not just main/master
- Provides individual performance ratings (0-5)
- Sends formatted report to Slack

### 2. Weekly Analysis
```bash
# Analyze last 7 days
python3 daily_analyzer.py --days-back 7

# Analyze specific week (Monday to Sunday)
python3 daily_analyzer.py --date-range 2025-08-11:2025-08-17
```
**What it does:**
- Aggregates commits across the entire week
- Shows cross-project activity patterns
- Provides comprehensive weekly performance overview

### 3. Specific Date Analysis
```bash
# Analyze a specific date
python3 daily_analyzer.py --date 2025-08-15

# Analyze last 3 days
python3 daily_analyzer.py --days-back 3
```

## 🤖 Automated Daily Reports (Cron)

### Setup Automated Daily Reports
```bash
# 1. Test the cron script first
python3 cron_daily_analysis.py --test

# 2. Edit crontab
crontab -e

# 3. Add this line for daily 9:00 AM reports
0 9 * * * cd /path/to/git-analyzer && python3 cron_daily_analysis.py

# 4. Verify it's installed
crontab -l
```

### Cron Job Examples
```bash
# Daily at 9:00 AM
0 9 * * * cd /path/to/git-analyzer && python3 cron_daily_analysis.py

# Monday-Friday at 9:00 AM (weekdays only)
0 9 * * 1-5 cd /path/to/git-analyzer && python3 cron_daily_analysis.py

# Daily at 9:00 AM with logging
0 9 * * * cd /path/to/git-analyzer && python3 cron_daily_analysis.py >> logs/cron_output.log 2>&1
```

## Testing and Configuration

### Test Slack Connection
```bash
# Test if Slack integration is working
python3 daily_analyzer.py --test-slack
```

### Use Custom Configuration
```bash
# Use a different config file
python3 daily_analyzer.py --config my_config.json --date 2025-08-15
```

## Cron Job Examples

### Test Cron Script
```bash
# Test the cron job script
python3 cron_daily_analysis.py --test

# Get cron installation instructions
python3 cron_daily_analysis.py --install-help
```

### Setup Cron Job
```bash
# Edit crontab
crontab -e

# Add this line to run daily at 9:00 AM
0 9 * * * cd /path/to/git-analyzer && python3 cron_daily_analysis.py

# Verify cron job is installed
crontab -l
```

## Multi-Project Analysis Results

When analyzing multiple projects, you'll see:

### Summary Statistics
```
Total commits: 46
Active developers: 7
Projects involved: 4
Files changed: 131
Lines added: 765
Lines deleted: 287
Net lines: 478
```

### Cross-Project Developer Activity
```
Developer: Dmytro Marjan
  Commits: 9
  Projects: LC App, Talent Point Frontend
  Files changed: 11
  Lines: +216/-86
```

### AI Analysis with Ratings
```
**INDIVIDUAL DEVELOPER PERFORMANCE**:

1. **Dmytro Marjan**
   - Summary: Worked across LC App and Talent Point Frontend, focusing on refactoring and optimization.
   - Performance Rating: 4/5
   - Key Focus: Code refactoring and cross-project coordination.
```

## Output Files

### Local Reports
Reports are saved to `./reports/` directory:
- `daily_report_2025-08-15.txt` - Single day report
- `daily_report_2025-08-14 to 2025-08-15.txt` - Date range report

### Logs
System logs are saved to `./logs/` directory:
- `dev_analyzer.log` - Main application logs
- `cron_daily_analysis.log` - Cron job specific logs

## Configuration Examples

### Enable/Disable Projects
```json
{
  "projects": [
    {
      "name": "Active Project",
      "enabled": true
    },
    {
      "name": "Disabled Project", 
      "enabled": false
    }
  ]
}
```

### Developer Mapping
```json
{
  "developer_mapping": {
    "global": {
      "username1": "Real Name",
      "alias1": "Real Name",
      "old_username": "New Name"
    }
  }
}
```

### Slack Configuration
```json
{
  "slack": {
    "enabled": true,
    "require_ai_success": true,
    "token": "xoxb-...",
    "channel_id": "C..."
  }
}
```

## Troubleshooting

### Check Repository Access
```bash
# Verify all repositories can be accessed
python3 setup.py --test-only
```

### View Recent Logs
```bash
# Check for errors
tail -f logs/dev_analyzer.log

# Check cron job logs
tail -f logs/cron_daily_analysis.log
```

### Manual Repository Update
If repositories fail to update automatically:
```bash
# Navigate to repository directory
cd repositories/project_name

# Manual git pull
git pull origin main
```
