#!/bin/bash

echo "🚀 Running Comprehensive User Mapping"
echo "====================================="

# Check if .env file exists
if [ -f .env ]; then
    echo "✅ Found .env file, loading environment variables..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ No .env file found!"
    echo "Please create a .env file with your API keys:"
    echo ""
    echo "# Jira API Keys"
    echo "JIRA_API_KEY=your_jira_api_key"
    echo "JIRA_EMAIL=<EMAIL>"
    echo "JIRA_ALEANNLAB_API_KEY=ATATT3xFfGF0EvE_d87QsesavsDdTiceSkANhr1iELtItSrEJ6os5wUpk46URlU8B4kqVyTgteap36098hfl1v4Ei3auMICf26TcZmKZtehe9MaBJTg7kBcL3Hdb4VKwzCR8QTxzMzlfHtJKq3ZPDokRELRVlniXrzrWN17kDCvAd_JIIXvOWGs=AA5FC415"
    echo "JIRA_ALEANNLAB_EMAIL=<EMAIL>"
    echo ""
    echo "# Tempo API Keys"
    echo "TEMPO_API_KEY=your_tempo_api_key"
    echo "TEMPO_CONVX_API_KEY=your_convx_tempo_key"
    echo "TEMPO_TOPPROPERTY_API_KEY=your_topproperty_tempo_key"
    echo "TEMPO_ALEANNLAB_API_KEY=your_aleannlab_tempo_key"
    exit 1
fi

# Check for required environment variables
required_vars=(
    "JIRA_API_KEY"
    "JIRA_EMAIL"
    "JIRA_ALEANNLAB_API_KEY"
    "JIRA_ALEANNLAB_EMAIL"
    "TEMPO_API_KEY"
    "TEMPO_CONVX_API_KEY"
    "TEMPO_TOPPROPERTY_API_KEY"
    "TEMPO_ALEANNLAB_API_KEY"
)

missing_vars=()
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "⚠️ Missing environment variables:"
    printf '   %s\n' "${missing_vars[@]}"
    echo ""
    echo "The script will continue but may not be able to fetch data from all instances."
    echo ""
fi

echo "🔍 Environment check completed"
echo ""

# Run the Python script
python3 comprehensive_user_mapping.py

echo ""
echo "✅ User mapping script completed!"
echo ""
echo "Next steps:"
echo "1. Review the updated config.json file"
echo "2. Test with: python3 cron_daily_analysis.py --test"
echo "3. Check your Slack channel for proper time tracking"
