#!/usr/bin/env python3
"""
Script to show the current Tempo user mappings and verify they match the discovered users.
"""

import json
import os
import requests
from datetime import datetime, timedelta

def load_config():
    """Load configuration from config.json"""
    with open('config.json', 'r') as f:
        return json.load(f)

def get_tempo_activity_summary():
    """Get a summary of Tempo activity for mapped users"""
    tempo_key = os.getenv('TEMPO_ALEANNLAB_API_KEY')
    if not tempo_key:
        print("❌ TEMPO_ALEANNLAB_API_KEY not found")
        return {}
    
    # Calculate date range (last 30 days)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    headers = {
        'Authorization': f'Bearer {tempo_key}',
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    }
    
    params = {
        'from': start_date.strftime('%Y-%m-%d'),
        'to': end_date.strftime('%Y-%m-%d'),
        'limit': 1000
    }
    
    try:
        url = "https://api.tempo.io/4/worklogs"
        response = requests.get(url, headers=headers, params=params, timeout=15)
        
        if response.status_code == 200:
            data = response.json()
            worklogs = data.get('results', [])
            
            # Summarize by account ID
            activity = {}
            for worklog in worklogs:
                author = worklog.get('author', {})
                account_id = author.get('accountId')
                if account_id:
                    if account_id not in activity:
                        activity[account_id] = {
                            'worklogs': 0,
                            'hours': 0
                        }
                    activity[account_id]['worklogs'] += 1
                    activity[account_id]['hours'] += worklog.get('timeSpentSeconds', 0) / 3600
            
            # Round hours
            for data in activity.values():
                data['hours'] = round(data['hours'], 2)
            
            return activity
        else:
            print(f"❌ Failed to fetch Tempo data: {response.status_code}")
            return {}
            
    except Exception as e:
        print(f"❌ Error fetching Tempo data: {e}")
        return {}

def main():
    print("🔍 Current Tempo User Mappings")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    tempo_mapping = config.get('developer_mapping', {}).get('tempo', {})
    
    if not tempo_mapping:
        print("❌ No Tempo mappings found in config.json")
        return
    
    print(f"📋 Found {len(tempo_mapping)} Tempo user mappings:")
    print()
    
    # Get activity summary
    print("📊 Fetching recent activity summary...")
    activity = get_tempo_activity_summary()
    print()
    
    # Show mappings with activity
    aleannlab_users = {}
    for account_id, name in tempo_mapping.items():
        # Filter for aleannlab users (those we just discovered)
        if account_id.startswith('712020:') or account_id == '63317599140ba0bf651afd28':
            aleannlab_users[account_id] = name
    
    print("🏢 Aleannlab Workspace Users:")
    print("-" * 30)
    
    for account_id, name in aleannlab_users.items():
        activity_info = activity.get(account_id, {'worklogs': 0, 'hours': 0})
        print(f"👤 {name}")
        print(f"   Account ID: {account_id}")
        print(f"   Last 30 days: {activity_info['worklogs']} worklogs, {activity_info['hours']} hours")
        print()
    
    print("📝 Configuration Summary:")
    print("-" * 25)
    print(f"✅ Total Tempo mappings: {len(tempo_mapping)}")
    print(f"✅ Aleannlab users: {len(aleannlab_users)}")
    print(f"✅ Other workspace users: {len(tempo_mapping) - len(aleannlab_users)}")
    
    print("\n🔧 To test these mappings, run your git analyzer with the updated configuration!")

if __name__ == "__main__":
    main()
