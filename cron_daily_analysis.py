#!/usr/bin/env python3
"""
Cron Job Script for Daily Developer Performance Analysis
Designed to run as a scheduled cron job at 9:00 AM daily
"""

import os
import sys
import logging
import traceback
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path to import our modules
script_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(script_dir))

try:
    from daily_analyzer import DailyAnalyzer
except ImportError as e:
    print(f"Error importing modules: {e}")
    print(f"Script directory: {script_dir}")
    print(f"Python path: {sys.path}")
    sys.exit(1)


def setup_cron_logging(log_dir: str = "./logs") -> logging.Logger:
    """Setup logging specifically for cron job execution"""
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Create a specific log file for cron jobs
    log_file = log_path / "cron_daily_analysis.log"
    
    # Configure logging with rotation
    logger = logging.getLogger('cron_daily_analysis')
    logger.setLevel(logging.INFO)
    
    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # Console handler (for cron output)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def check_environment() -> bool:
    """Check if the environment is properly set up for the cron job"""
    logger = logging.getLogger('cron_daily_analysis')
    
    # Check if config file exists
    config_file = script_dir / "config.json"
    if not config_file.exists():
        logger.error(f"Configuration file not found: {config_file}")
        return False
    
    # Check if required Python modules are available
    required_modules = ['requests', 'json', 'subprocess']
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            logger.error(f"Required Python module not available: {module}")
            return False
    
    # Check if git is available
    import subprocess
    try:
        result = subprocess.run(['git', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            logger.error("Git is not available or not working properly")
            return False
        logger.info(f"Git version: {result.stdout.strip()}")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        logger.error("Git command not found or timed out")
        return False
    
    logger.info("Environment check passed")
    return True


def run_cron_analysis() -> int:
    """Main function for cron job execution"""
    # Change to script directory to ensure relative paths work
    os.chdir(script_dir)
    
    # Setup logging
    logger = setup_cron_logging()
    
    logger.info("=" * 60)
    logger.info("CRON DAILY ANALYSIS STARTED")
    logger.info("=" * 60)
    logger.info(f"Script directory: {script_dir}")
    logger.info(f"Working directory: {os.getcwd()}")
    logger.info(f"Python executable: {sys.executable}")
    logger.info(f"Python version: {sys.version}")
    
    try:
        # Check environment
        if not check_environment():
            logger.error("Environment check failed")
            return 1
        
        # Initialize and run analyzer
        logger.info("Initializing Daily Analyzer...")
        analyzer = DailyAnalyzer("config.json")
        
        # Run the daily analysis
        logger.info("Starting daily analysis workflow...")
        success = analyzer.run_daily_analysis()
        
        if success:
            logger.info("Daily analysis completed successfully")
            logger.info("=" * 60)
            logger.info("CRON DAILY ANALYSIS COMPLETED SUCCESSFULLY")
            logger.info("=" * 60)
            return 0
        else:
            logger.error("Daily analysis failed")
            logger.error("=" * 60)
            logger.error("CRON DAILY ANALYSIS FAILED")
            logger.error("=" * 60)
            return 1
            
    except Exception as e:
        logger.error(f"Unexpected error during cron analysis: {str(e)}")
        logger.error("Traceback:")
        logger.error(traceback.format_exc())
        logger.error("=" * 60)
        logger.error("CRON DAILY ANALYSIS FAILED WITH EXCEPTION")
        logger.error("=" * 60)
        return 1


def install_cron_job():
    """Helper function to install the cron job"""
    print("🤖 AUTOMATED DAILY REPORTS SETUP")
    print("=" * 50)
    print()
    print("📅 To get daily developer reports at 9:00 AM:")
    print()
    print("1️⃣ Test the cron script first:")
    print(f"   cd {script_dir}")
    print("   python3 cron_daily_analysis.py --test")
    print()
    print("2️⃣ Open your crontab:")
    print("   crontab -e")
    print()
    print("3️⃣ Add this line for daily reports at 9:00 AM:")
    print(f"   0 9 * * * cd {script_dir} && python3 cron_daily_analysis.py")
    print()
    print("4️⃣ Save and exit the editor (Ctrl+X, then Y, then Enter)")
    print()
    print("5️⃣ Verify the cron job is installed:")
    print("   crontab -l")
    print()
    print("📋 Alternative schedules:")
    print("   # Weekdays only (Monday-Friday)")
    print(f"   0 9 * * 1-5 cd {script_dir} && python3 cron_daily_analysis.py")
    print()
    print("   # With detailed logging")
    print(f"   0 9 * * * cd {script_dir} && python3 cron_daily_analysis.py >> logs/cron_output.log 2>&1")
    print()
    print("🔍 Monitor your reports:")
    print(f"   tail -f {script_dir}/logs/cron_daily_analysis.log")
    print()
    print("✅ That's it! You'll get daily reports in Slack at 9:00 AM!")


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Cron job script for daily developer analysis')
    parser.add_argument('--install-help', action='store_true', 
                       help='Show instructions for installing as a cron job')
    parser.add_argument('--test', action='store_true',
                       help='Run a test analysis to verify everything works')
    
    args = parser.parse_args()
    
    if args.install_help:
        install_cron_job()
        return 0
    
    if args.test:
        print("Running test analysis...")
        print("This will analyze yesterday's commits and test all components.")
        print()
        
        # Change to script directory
        os.chdir(script_dir)
        
        # Setup basic logging for test
        logging.basicConfig(level=logging.INFO, 
                          format='%(asctime)s - %(levelname)s - %(message)s')
        
        try:
            analyzer = DailyAnalyzer("config.json")
            
            # Test Slack connection
            print("Testing Slack connection...")
            if analyzer.slack_notifier.test_connection():
                print("✅ Slack connection successful")
            else:
                print("❌ Slack connection failed")
            
            # Test repository access
            print("Testing repository access...")
            if analyzer.ensure_repositories():
                print("✅ Repository access successful")
            else:
                print("❌ Repository access failed")
            
            # Run analysis
            print("Running analysis...")
            success = analyzer.run_daily_analysis()
            
            if success:
                print("✅ Test analysis completed successfully")
                return 0
            else:
                print("❌ Test analysis failed")
                return 1
                
        except Exception as e:
            print(f"❌ Test failed with error: {str(e)}")
            return 1
    
    # Default: run the cron analysis
    return run_cron_analysis()


if __name__ == "__main__":
    sys.exit(main())
