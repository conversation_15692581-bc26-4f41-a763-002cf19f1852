#!/usr/bin/env python3
"""Debug the complete analysis workflow for <PERSON><PERSON><PERSON><PERSON>"""

import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from git_analyzer import MultiProjectGitAnalyzer
from jira_integration import <PERSON>raTempoAnalyzer

def debug_full_analysis():
    """Debug the complete analysis workflow"""
    print("🔍 Debugging Full Analysis Workflow...")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # Initialize components
    git_analyzer = MultiProjectGitAnalyzer(config)
    jira_tempo_analyzer = JiraTempoAnalyzer(config)
    
    target_date = datetime.strptime('2025-08-15', '%Y-%m-%d').date()
    
    print(f"📅 Target Date: {target_date}")
    
    # Get commits
    commits = git_analyzer.get_commits_with_changes(target_date)
    print(f"📊 Total Commits: {len(commits)}")
    
    # Find <PERSON><PERSON><PERSON><PERSON>'s commits
    rostyslav_commits = [c for c in commits if 'rostyslf' in c.get('author', '').lower()]
    print(f"👤 Rostyslav Commits: {len(rostyslav_commits)}")
    for commit in rostyslav_commits:
        print(f"  - {commit.get('author', 'Unknown')} in {commit.get('repository', 'Unknown')}")
    
    # Run Jira/Tempo analysis
    print(f"\n🔄 Running Jira/Tempo Analysis...")
    print("=" * 40)
    
    analysis_results = jira_tempo_analyzer.analyze_developer_tickets_and_time(
        commits, target_date, target_date
    )
    
    print(f"Analysis Results: {len(analysis_results)} developers")
    
    for developer, data in analysis_results.items():
        print(f"\n👤 Developer: {developer}")
        print(f"  Has Commits: {data.get('has_commits', False)}")
        print(f"  Has Time Logs: {data.get('has_time_logs', False)}")
        
        commit_tickets = data.get('commit_tickets', [])
        print(f"  Commit Tickets: {commit_tickets}")
        
        tempo_data = data.get('tempo_data', {})
        hours_logged = tempo_data.get('total_hours_logged', 0)
        print(f"  Tempo Hours: {hours_logged}")
        
        if developer.lower() == 'rostyslav f':
            print(f"  🎯 ROSTYSLAV F DETAILS:")
            print(f"    Tempo Data Keys: {list(tempo_data.keys())}")
            for key, value in tempo_data.items():
                print(f"    {key}: {value}")

if __name__ == "__main__":
    debug_full_analysis()